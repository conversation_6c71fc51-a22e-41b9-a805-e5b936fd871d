# 腾讯云COS存储集成说明

## 概述

本系统已集成腾讯云COS（对象存储）服务，用于存储微信聊天图片和小程序码等文件。

## 配置信息

### 腾讯云COS配置
- **SecretId**: AKID2uXu2wbz7yYrteDWYJn1yf5D5h38yXqs
- **SecretKey**: oLbbOvCm6AxUkJgneFafT5VhdLp33pNg
- **存储桶名称**: mp-1373032532
- **地域**: ap-chongqing（重庆）
- **文件链接前缀**: https://mp-1373032532.cos.ap-chongqing.myqcloud.com/

## 功能特性

### 1. 自动上传微信聊天图片
- 用户发送的图片会自动下载并上传到COS
- 存储路径：`images/YYYY-MM-DD/timestamp-random-user-image.jpg`
- 数据库中存储COS的完整URL

### 2. 客服上传图片
- 客服发送的图片会直接上传到COS
- 存储路径：`customer-service/timestamp-random-filename.ext`
- 前端使用COS的完整URL显示图片

### 3. 自动上传小程序码
- 生成的小程序码会自动上传到COS
- 存储路径：`qrcode/qrcode_configId_timestamp.png`
- 数据库中存储COS的完整URL

### 4. 自动上传语音消息
- 用户发送的语音消息会自动下载并上传到COS
- 存储路径：`voices/YYYY-MM-DD/timestamp-random-user-voice.format`
- 数据库中存储COS的完整URL

### 5. 客服上传语音
- 客服发送的语音会直接上传到COS
- 存储路径：`customer-service-voice/timestamp-random-filename.ext`
- 前端使用COS的完整URL播放语音

### 6. 用户头像上传
- 用户头像会直接上传到COS
- 存储路径：`avatars/timestamp-random-filename.ext`
- 数据库中存储COS的完整URL

### 7. 通用文件上传接口
- 新增 `/common/upload-cos` 接口支持直接上传文件到COS
- 支持自定义文件夹路径
- 返回COS文件URL和key

## 文件结构

```
servers/src/
├── shared/
│   └── cos.service.ts          # COS服务类
├── modules/
│   ├── wx/
│   │   ├── wx-mp/
│   │   │   └── wx-mp.service.ts    # 小程序码上传到COS
│   │   └── wx-service/
│   │       └── wx-service.service.ts # 聊天图片上传到COS
│   └── common/
│       └── upload/
│           └── upload.controller.ts # 通用上传接口
├── config/
│   ├── config.dev.ts          # 开发环境COS配置
│   └── config.pro.ts          # 生产环境COS配置
└── test/
    └── cos.test.ts             # COS服务测试
```

## API接口

### 1. 通用文件上传到COS
```
POST /common/upload-cos
Content-Type: multipart/form-data

参数:
- file: 上传的文件
- folder: 可选，自定义文件夹路径

返回:
{
  "success": true,
  "url": "https://mp-1373032532.cos.ap-chongqing.myqcloud.com/folder/filename",
  "key": "folder/filename",
  "originalname": "原始文件名",
  "mimetype": "文件类型",
  "size": 文件大小
}
```

## COS服务方法

### CosService 主要方法

1. **uploadFile(fileBuffer, fileName, folder?)** - 通用文件上传
2. **uploadImage(fileBuffer, originalName, folder?)** - 图片上传
3. **uploadQrcode(fileBuffer, fileName)** - 小程序码上传
4. **uploadAvatar(fileBuffer, originalName)** - 头像上传
5. **uploadVoice(fileBuffer, originalName, folder?)** - 语音上传
6. **deleteFile(key)** - 删除文件
7. **fileExists(key)** - 检查文件是否存在
8. **extractKeyFromUrl(url)** - 从URL提取COS key

## 环境变量配置

可以通过环境变量覆盖默认配置：

```bash
# .env 文件
COS_SECRET_ID=你的SecretId
COS_SECRET_KEY=你的SecretKey
COS_BUCKET=你的存储桶名称
COS_REGION=你的地域
COS_DOMAIN=你的COS域名
```

## 测试

运行COS服务测试：
```bash
npm test -- cos.test.ts
```

## 注意事项

1. **安全性**: SecretId和SecretKey已配置在代码中，生产环境建议使用环境变量
2. **存储路径**: 文件会按日期和类型自动分类存储
3. **文件命名**: 使用时间戳和随机数确保文件名唯一性
4. **错误处理**: 所有上传操作都有完整的错误处理和日志记录
5. **兼容性**: 保持与原有本地存储的兼容性，可以平滑迁移

## 迁移说明

系统已自动切换到COS存储，无需手动迁移。新产生的文件会自动存储到COS，原有的本地文件仍可正常访问。

## 监控和维护

- 查看COS控制台监控文件上传情况
- 定期清理测试文件
- 监控存储用量和费用
