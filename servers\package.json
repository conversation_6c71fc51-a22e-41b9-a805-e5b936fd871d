{"name": "meimei-new", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "dev": "nest start -b swc -w", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:pull": "prisma db pull", "db:m": "prisma migrate dev --name inte", "db:g": "prisma generate"}, "dependencies": {"@nestjs-modules/ioredis": "^2.0.2", "@nestjs/bullmq": "^10.2.2", "@nestjs/common": "^10.3.8", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.8", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.8", "@nestjs/throttler": "^5.1.2", "@prisma/client": "5.12.1", "@types/ua-parser-js": "^0.7.39", "axios": "^1.6.8", "bcrypt": "^5.1.1", "bullmq": "^5.25.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cos-nodejs-sdk-v5": "^2.15.4", "cron-parser": "^5.3.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "ioredis": "^5.4.1", "lodash": "^4.17.21", "mime": "3", "nanoid": "3.3.4", "nestjs-prisma": "^0.23.0", "node-xlsx": "^0.24.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^5.12.1", "redis-info": "^3.1.0", "reflect-metadata": "^0.2.2", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "svg-captcha": "^1.4.0", "systeminformation": "^5.22.9", "ua-parser-js": "^1.0.37", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.8", "@swc/cli": "^0.3.12", "@swc/core": "^1.4.16", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.13", "@types/jest": "29.5.12", "@types/lodash": "^4.17.20", "@types/multer": "^1.4.11", "@types/node": "^20.12.7", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^9.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "29.7.0", "prettier": "^3.2.5", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.2", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.4.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}