-- Create WxMpConfig table
CREATE TABLE `wx_mp_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `username` varchar(64) NOT NULL,
  `serviceType` char(1) NOT NULL,
  `auditStatus` char(1) NOT NULL DEFAULT '0',
  `status` char(1) NOT NULL DEFAULT '0',
  `serverUrl` varchar(255) NULL,
  `token` varchar(100) NOT NULL,
  `encodingAes<PERSON>ey` varchar(100) NOT NULL,
  `appId` varchar(100) NOT NULL,
  `appSecret` varchar(100) NOT NULL,
  `qrcode` varchar(255) NULL,
  `createBy` varchar(64) NOT NULL,
  `createTime` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updateBy` varchar(64) NULL,
  `updateTime` datetime(3) NULL,
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;