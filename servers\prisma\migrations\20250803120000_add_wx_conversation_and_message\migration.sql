-- CreateTable
CREATE TABLE `wx_conversation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `configId` INTEGER NOT NULL,
    `openId` VARCHAR(100) NOT NULL,
    `userName` VARCHAR(100) NULL,
    `lastMessage` TEXT NULL,
    `lastMessageTime` DATETIME(3) NULL,
    `unreadCount` INTEGER NOT NULL DEFAULT 0,
    `status` CHAR(1) NOT NULL DEFAULT '1',
    `createTime` DATETIME(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateTime` DATETIME(3) NULL,

    INDEX `wx_conversation_configId_idx`(`configId`),
    INDEX `wx_conversation_lastMessageTime_idx`(`lastMessageTime`),
    UNIQUE INDEX `wx_conversation_configId_openId_key`(`configId`, `openId`),
    PRIMARY <PERSON>EY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `wx_message` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `configId` INTEGER NOT NULL,
    `conversationId` INTEGER NOT NULL,
    `openId` VARCHAR(100) NOT NULL,
    `fromUser` BOOLEAN NOT NULL,
    `messageType` VARCHAR(20) NOT NULL DEFAULT 'text',
    `content` TEXT NOT NULL,
    `mediaId` VARCHAR(100) NULL,
    `createTime` DATETIME(3) NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `wx_message_configId_idx`(`configId`),
    INDEX `wx_message_conversationId_idx`(`conversationId`),
    INDEX `wx_message_openId_idx`(`openId`),
    INDEX `wx_message_createTime_idx`(`createTime`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `wx_conversation` ADD CONSTRAINT `wx_conversation_configId_fkey` FOREIGN KEY (`configId`) REFERENCES `wx_mp_config`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `wx_message` ADD CONSTRAINT `wx_message_configId_fkey` FOREIGN KEY (`configId`) REFERENCES `wx_mp_config`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `wx_message` ADD CONSTRAINT `wx_message_conversationId_fkey` FOREIGN KEY (`conversationId`) REFERENCES `wx_conversation`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
