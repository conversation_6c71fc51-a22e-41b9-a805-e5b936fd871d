# 删除线字体功能 - Prisma 迁移说明

## 迁移概述

本次迁移为 `wx_mp_config` 表添加了 `strikethroughEnabled` 字段，用于控制客服消息的删除线显示效果。

## 迁移文件

- **文件名**: `20250805120000_add_strikethrough_enabled_to_wx_mp_config`
- **创建时间**: 2025-08-05 12:00:00
- **迁移类型**: ALTER TABLE

## 字段详情

- **字段名**: `strikethroughEnabled`
- **类型**: `BOOLEAN`
- **默认值**: `true`
- **是否可空**: `NOT NULL`
- **说明**: 控制客服发送的文字消息是否显示删除线效果

## 执行迁移

### 方法1: 使用 Prisma CLI（推荐）

```bash
cd servers
npx prisma db push
```

### 方法2: 生成并应用迁移

```bash
cd servers
# 生成迁移文件（如果需要重新生成）
npx prisma migrate dev --name add_strikethrough_enabled_to_wx_mp_config

# 应用迁移到生产环境
npx prisma migrate deploy
```

### 方法3: 手动执行 SQL

```sql
ALTER TABLE `wx_mp_config` ADD COLUMN `strikethroughEnabled` BOOLEAN NOT NULL DEFAULT true;
```

## 验证迁移

执行以下命令验证字段是否成功添加：

```sql
DESCRIBE `wx_mp_config`;
```

或者查询表结构：

```sql
SHOW COLUMNS FROM `wx_mp_config` LIKE 'strikethroughEnabled';
```

## 回滚迁移

如果需要回滚此迁移，可以执行：

```sql
ALTER TABLE `wx_mp_config` DROP COLUMN `strikethroughEnabled`;
```

## 注意事项

1. **默认值**: 新字段默认为 `true`，确保现有配置启用删除线功能
2. **数据兼容性**: 此迁移不会影响现有数据
3. **应用重启**: 迁移完成后需要重启应用服务
4. **权限检查**: 确保数据库用户有 ALTER TABLE 权限

## 相关文件

- Schema 定义: `servers/prisma/schema.prisma`
- 迁移 SQL: `servers/prisma/migrations/20250805120000_add_strikethrough_enabled_to_wx_mp_config/migration.sql`
- 手动 SQL: `servers/add_strikethrough_enabled_field.sql`
