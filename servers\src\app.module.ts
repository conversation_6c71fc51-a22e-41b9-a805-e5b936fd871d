/*
 * @Author: sss
 * @Date: 2024-04-22 08:52:21
 * @LastEditors: sss
 * @LastEditTime: 2024-04-30 14:44:17
 * @FilePath: \meimei-new\src\app.module.ts
 * @Description:根模块
 *
 */
import { Module } from '@nestjs/common';
import { SharedModule } from './shared/shared.module';
import { AppController } from './app.controller';
import { SysModule } from './modules/sys/sys.module';
import { LoginModule } from './modules/login/login.module';
import { MonitorModule } from './modules/monitor/monitor.module';
import { CommonModule } from './modules/common/common.module';
import { WxMpModule } from './modules/wx/wx-mp/wx-mp.module';
import { WxApiModule } from './modules/wx/wx-api/wx-api.module';
import { WxServiceModule } from './modules/wx/wx-service/wx-service.module';

@Module({
  imports: [SharedModule, CommonModule, SysModule, LoginModule, MonitorModule, WxMpModule, WxApiModule, WxServiceModule],
  controllers: [AppController],
})
export class AppModule {}

