/*
https://docs.nestjs.com/controllers#controllers
*/

import {
  Controller,
  Post,
  Query,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { CosService } from '../../../shared/cos.service';
@Controller('common')
export class UploadController {
  constructor(private readonly cosService: CosService) {}
  /* 单文件上传 */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Query('fileName') fileName,
  ) {
    return {
      fileName,
      originalname: file.originalname,
      mimetype: file.mimetype,
    };
  }

  /* 单文件上传到COS */
  @Post('upload-cos')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileToCos(
    @UploadedFile() file: Express.Multer.File,
    @Query('folder') folder?: string,
  ) {
    if (!file) {
      throw new Error('没有上传文件');
    }

    const uploadResult = await this.cosService.uploadFile(
      file.buffer,
      file.originalname,
      folder
    );

    if (!uploadResult.success) {
      throw new Error(`上传失败: ${uploadResult.error}`);
    }

    return {
      success: true,
      url: uploadResult.url,
      key: uploadResult.key,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    };
  }

  /* 数组文件上传 */
  @Post('uploads')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFils(@UploadedFiles() files: Array<Express.Multer.File>) {
    /* 暂未处理 */
    return files;
  }
}
