import { Controller, Get, Post, Body, Param, Query, Headers, HttpCode, Res, Req, RawBodyRequest } from '@nestjs/common';
import { Response, Request } from 'express';
import { WxApiService } from './wx-api.service';
import { Public } from 'src/common/decorators/public.decorator';

@Controller('wx/wx-api')
export class WxApiController {
  constructor(private readonly wxApiService: WxApiService) { }

  // 微信服务器验证
  @Get(':token')
  @Public()
  async verify(
    @Param('token') token: string,
    @Query('signature') signature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Query('echostr') echostr: string,
    @Res() res: Response,
  ) {
    const result = await this.wxApiService.verify(token, signature, timestamp, nonce, echostr);

    // 设置响应头为纯文本
    res.setHeader('Content-Type', 'text/plain');

    // 直接发送纯文本响应，不进行JSON包装
    res.send(result);
  }

  // 接收微信消息推送
  @Post(':token')
  @Public()
  @HttpCode(200)
  async receiveMessage(
    @Param('token') token: string,
    @Body() body: any,
    @Headers('content-type') contentType: string,
    @Query() query: any,
    @Req() req: Request,
  ) {
    // 尝试多种方式获取原始body数据
    let rawBody = body;

    // 方法1: 检查req.rawBody (NestJS可能提供)
    if ((req as any).rawBody) {
      rawBody = (req as any).rawBody.toString('utf8');
      console.log('使用req.rawBody');
    }
    // 方法2: 检查req.body是否为Buffer
    else if (Buffer.isBuffer(req.body)) {
      rawBody = req.body.toString('utf8');
      console.log('使用Buffer格式的req.body');
    }
    // 方法3: 如果body是字符串，直接使用
    else if (typeof body === 'string') {
      rawBody = body;
      console.log('使用字符串格式的body');
    }
    // 方法4: 如果body是对象但为空，可能需要特殊处理
    else if (body && typeof body === 'object' && Object.keys(body).length === 0) {
      console.log('检测到空对象body，可能需要配置原始body解析');
    }

    console.log('最终使用的body类型:', typeof rawBody);
    console.log('Content-Type:', contentType);

    return await this.wxApiService.handleMessage(token, rawBody, contentType, query);
  }
}
