import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { WxApiController } from './wx-api.controller';
import { WxApiService } from './wx-api.service';
import { WxServiceModule } from '../wx-service/wx-service.module';
import * as express from 'express';

@Module({
  imports: [WxServiceModule],
  controllers: [WxApiController],
  providers: [WxApiService],
})
export class WxApiModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 为微信API路由配置原始body解析中间件
    consumer
      .apply(
        express.raw({ type: 'text/xml', limit: '10mb' }),
        express.raw({ type: 'application/xml', limit: '10mb' }),
        express.text({ type: 'text/xml', limit: '10mb' }),
        express.text({ type: 'application/xml', limit: '10mb' }),
        (req: any, res: any, next: any) => {
          // 将原始body保存到req.rawBody
          if (req.body && (Buffer.isBuffer(req.body) || typeof req.body === 'string')) {
            req.rawBody = req.body.toString('utf8');
            console.log('中间件 - 保存原始body，长度:', req.rawBody.length);
          }
          next();
        }
      )
      .forRoutes(WxApiController);
  }
}