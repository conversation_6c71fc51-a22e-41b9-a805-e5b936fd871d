import { Injectable } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as xml2js from 'xml2js';
import { WxServiceService } from '../wx-service/wx-service.service';

@Injectable()
export class WxApiService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly wxServiceService: WxServiceService,
  ) { }

  // 验证微信服务器
  async verify(urlToken: string, signature: string, timestamp: string, nonce: string, echostr: string) {
    // console.log('收到验证请求 ->', {
    //   urlToken,
    //   signature,
    //   timestamp,
    //   nonce,
    //   echostr
    // });

    // 根据URL中的token参数查找配置
    const config = await this.findConfigByToken(urlToken);
    if (!config) {
      // console.log('Token校验失败：未找到对应配置，URL token:', urlToken);
      return 'Token校验失败，请检查确认';
    }

    // console.log('找到配置，使用数据库中的token进行验证:', config.token.substring(0, 8) + '...');

    // 使用数据库中的token字段进行签名验证
    const timestampStr = String(timestamp);
    const nonceStr = String(nonce);
    const dbToken = String(config.token);

    // 字典序排序
    const tmpArr = [dbToken, timestampStr, nonceStr];
    tmpArr.sort();
    const tmpStr = tmpArr.join('');

    // sha1 加密
    const tmpSha = crypto.createHash('sha1').update(tmpStr, 'utf8').digest('hex');

    // console.log('签名验证详情:', {
    //   'URL中的token': urlToken,
    //   '数据库中的token': dbToken.substring(0, 8) + '...',
    //   '生成的签名': tmpSha,
    //   '收到的签名': signature,
    //   '参与签名的字符串': tmpStr
    // });

    // 验证签名
    if (tmpSha === signature) {
      // console.log('Token 校验成功');
      return echostr;
    } else {
      // console.log('Token 校验失败');
      return 'Token校验失败，请检查确认';
    }
  }

  // 处理微信消息
  async handleMessage(token: string, body: any, contentType: string, query: any = {}) {
    try {
      // console.log('=== 收到微信消息推送 ===');
      // console.log('URL Token:', token);
      // console.log('Content-Type:', contentType);

      const config = await this.findConfigByToken(token);
      if (!config) {
        // console.log('处理消息失败：未找到配置');
        return this.buildXMLResponse('success'); // 返回 XML 格式的成功响应
      }

      // 提取 URL 参数
      const { timestamp, nonce, msg_signature, encrypt_type } = query;

      // 解析Body获取Encrypt字段
      let encryptData: string | undefined;

      if (typeof body === 'string') {
        try {
          const result = await xml2js.parseStringPromise(body);
          encryptData = result.xml?.Encrypt?.[0];
        } catch (error) {
          // console.log('XML解析失败:', error.message);
        }
      } else if (body && typeof body === 'object') {
        encryptData = body.Encrypt || body.encrypt;
      }

      // 检查是否为安全模式
      if (encrypt_type === 'aes' && msg_signature) {
        if (!encryptData) {
          // console.log('错误：安全模式下未找到Encrypt字段');
          return this.buildXMLResponse('success');
        }

        // 验证 msg_signature 签名
        const signatureValid = this.verifyMsgSignature(config.token, timestamp, nonce, encryptData, msg_signature);
        if (!signatureValid) {
          // console.log('消息签名验证失败');
          return this.buildXMLResponse('success');
        }
      }

      let message: any;

      // 2. 解密消息体
      if (encrypt_type === 'aes') {
        // console.log('进入安全模式解密');
        message = await this.decryptMessage(body, config, query);
        if (!message) {
          // console.log('消息解密失败');
          return this.buildXMLResponse('success'); // 解密失败返回success
        }
      } else {
        // 明文模式：直接解析
        // console.log('进入明文模式解析');
        if (typeof body === 'string') {
          const result = await xml2js.parseStringPromise(body);
          message = result.xml;
        } else {
          message = body;
        }
      }

      // 3. 处理不同类型的消息
      const msgType = message.MsgType?.[0] || '';
      let responseContent: string;

      switch (msgType) {
        case 'text':
          responseContent = await this.handleTextMessage(message, config);
          break;
        case 'image':
          responseContent = await this.handleImageMessage(message, config);
          break;
        case 'voice':
          responseContent = await this.handleVoiceMessage(message, config);
          break;
        case 'event':
          responseContent = await this.handleEventMessage(message, config);
          break;
        default:
          responseContent = 'success';
      }

      // 4. 构造加密后的回复
      const reply = await this.buildEncryptedReply(config, responseContent, nonce);
      return reply;

    } catch (error) {
      // console.error('处理微信消息失败:', error);
      return this.buildXMLResponse('success');
    }
  }

  private async findConfigByToken(urlToken: string) {
    // 从URL中提取真实token
    const tokenMatch = urlToken.match(/wx-api\/(.*?)$/);
    const realToken = tokenMatch ? tokenMatch[1] : urlToken;
    // console.log('提取的真实token:', realToken);

    // 构造完整的服务器URL
    const domain = this.configService.get<string>('domain');
    const serverUrl = `${domain}/wx/wx-api/${realToken}`;
    // console.log('构造的完整URL:', serverUrl);

    const config = await this.prisma.wxMpConfig.findFirst({
      where: {
        AND: [
          {
            OR: [
              { token: realToken },
              { serverUrl }
            ]
          },
          { status: '0' }
        ]
      }
    });

    if (!config) {
      // 输出调试信息
      const allConfigs = await this.prisma.wxMpConfig.findMany({
        select: {
          id: true,
          name: true,
          serverUrl: true,
          token: true,
          status: true
        }
      });
      
      // console.log('查找配置失败，数据库中的所有配置:', JSON.stringify(allConfigs, null, 2));
      // console.log('查找参数:', {
      //   realToken,
      //   serverUrl,
      //   originalToken: urlToken
      // });
      return null;
    }

    // console.log('找到匹配的配置:', {
    //   id: config.id,
    //   name: config.name,
    //   serverUrl: config.serverUrl,
    //   configToken: config.token,
    //   status: config.status
    // });

    return config;
  }

  // 解密微信消息（安全模式）
  private async decryptMessage(body: any, config: any, query: any) {
    try {
      const { timestamp, nonce, msg_signature } = query;
      let encryptData: string;

      // 提取加密数据
      if (typeof body === 'string') {
        const result = await xml2js.parseStringPromise(body);
        encryptData = result.xml.Encrypt[0];
      } else {
        encryptData = body.Encrypt;
      }

      // 验证消息签名
      if (!this.verifyMsgSignature(config.token, timestamp, nonce, encryptData, msg_signature)) {
        // console.log('消息签名验证失败');
        return null;
      }

      // 解密消息
      const decryptedMsg = this.decryptAES(encryptData, config.encodingAesKey, config.appId);
      if (!decryptedMsg) {
        // console.log('消息解密失败');
        return null;
      }

      // 解析解密后的消息
      let message: any;
      if (typeof decryptedMsg === 'string') {
        const result = await xml2js.parseStringPromise(decryptedMsg);
        message = result.xml;
      } else {
        message = decryptedMsg;
      }
      console.log('解密后的消息:', message);
      return message;
    } catch (error) {
      console.error('解密消息失败:', error);
      return null;
    }
  }

  // 验证消息签名（安全模式）
  private verifyMsgSignature(token: string, timestamp: string, nonce: string, encrypt: string, msgSignature: string): boolean {
    // 检查必要参数
    if (!token || !timestamp || !nonce || !encrypt || !msgSignature) {
      // console.log('签名验证失败：缺少必要参数');
      return false;
    }

    const tmpArr = [token, timestamp, nonce, encrypt];
    tmpArr.sort();
    const tmpStr = tmpArr.join('');
    const signature = crypto.createHash('sha1').update(tmpStr, 'utf8').digest('hex');

    const isValid = signature === msgSignature;
    if (!isValid) {
      // console.log('消息签名验证失败');
    }

    return isValid;
  }

  // AES解密 - 按照微信官方文档标准实现
  private decryptAES(encryptedData: string, encodingAesKey: string, appId: string): string | null {
    try {
      // 1. 生成AES密钥：EncodingAESKey + "=" 然后Base64解码
      const aesKey = Buffer.from(encodingAesKey + '=', 'base64');

      // 2. 将Encrypt密文进行Base64解码
      const encrypted = Buffer.from(encryptedData, 'base64');

      // 3. AES解密：CBC模式，IV使用AES密钥的前16字节
      const iv = aesKey.slice(0, 16);
      const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
      decipher.setAutoPadding(false); // 手动处理PKCS7填充

      let decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);

      // 4. 去除PKCS7填充
      const pad = decrypted[decrypted.length - 1];
      if (pad > 0 && pad <= 32) {
        decrypted = decrypted.slice(0, decrypted.length - pad);
      }

      // 5. 解析消息结构：random(16B) + msg_len(4B) + msg + appid
      if (decrypted.length < 20) {
        // console.log('解密数据长度不足');
        return null;
      }

      // 读取消息长度（网络字节序，大端序）
      const msgLen = decrypted.readUInt32BE(16);

      if (decrypted.length < 20 + msgLen) {
        // console.log('数据长度不足，无法提取完整消息');
        return null;
      }

      // 提取消息内容
      const msg = decrypted.slice(20, 20 + msgLen).toString('utf8');

      // 提取并验证AppId
      const receivedAppId = decrypted.slice(20 + msgLen).toString('utf8');

      if (receivedAppId !== appId) {
        // console.log('AppId验证失败，期望:', appId, '实际:', receivedAppId);
        return null;
      }

      return msg;
    } catch (error) {
      console.error('AES解密失败:', error);
      return null;
    }
  }

  private async handleTextMessage(message: any, config: any) {
    try {
      // 处理文本消息
      console.log('收到文本消息:', message);

      // 提取消息信息
      const openId = Array.isArray(message.FromUserName) ? message.FromUserName[0] : message.FromUserName;
      const content = Array.isArray(message.Content) ? message.Content[0] : message.Content;
      const msgId = Array.isArray(message.MsgId) ? message.MsgId[0] : message.MsgId;
      const createTime = Array.isArray(message.CreateTime) ? message.CreateTime[0] : message.CreateTime;

      // 保存消息到数据库
      await this.wxServiceService.receiveMessage(config.id, {
        openId,
        content,
        messageType: 'text',
        msgId,
        createTime: parseInt(createTime) * 1000 // 转换为毫秒时间戳
      });

      return 'success';
    } catch (error) {
      console.error('处理文本消息失败:', error);
      return 'success';
    }
  }

  private async handleImageMessage(message: any, config: any) {
    try {
      // 处理图片消息
      console.log('收到图片消息:', message);

      // 提取消息信息
      const openId = Array.isArray(message.FromUserName) ? message.FromUserName[0] : message.FromUserName;
      const mediaId = Array.isArray(message.MediaId) ? message.MediaId[0] : message.MediaId;
      const picUrl = Array.isArray(message.PicUrl) ? message.PicUrl[0] : message.PicUrl;
      const msgId = Array.isArray(message.MsgId) ? message.MsgId[0] : message.MsgId;
      const createTime = Array.isArray(message.CreateTime) ? message.CreateTime[0] : message.CreateTime;

      // 保存消息到数据库
      await this.wxServiceService.receiveMessage(config.id, {
        openId,
        content: picUrl || '[图片]', // 使用图片URL作为内容，如果没有则显示[图片]
        messageType: 'image',
        mediaId,
        msgId,
        createTime: parseInt(createTime) * 1000 // 转换为毫秒时间戳
      });

      console.log('图片消息已保存到数据库');
      return 'success';
    } catch (error) {
      console.error('处理图片消息失败:', error);
      return 'success';
    }
  }

  private async handleVoiceMessage(message: any, config: any) {
    try {
      // 处理语音消息
      console.log('收到语音消息:', message);

      // 提取消息信息
      const openId = Array.isArray(message.FromUserName) ? message.FromUserName[0] : message.FromUserName;
      const mediaId = Array.isArray(message.MediaId) ? message.MediaId[0] : message.MediaId;
      const format = Array.isArray(message.Format) ? message.Format[0] : message.Format;
      const recognition = Array.isArray(message.Recognition) ? message.Recognition[0] : message.Recognition; // 语音识别结果（如果开启了语音识别）
      const msgId = Array.isArray(message.MsgId) ? message.MsgId[0] : message.MsgId;
      const createTime = Array.isArray(message.CreateTime) ? message.CreateTime[0] : message.CreateTime;

      // 保存消息到数据库，使用mediaId作为content，后续会下载语音文件
      await this.wxServiceService.receiveMessage(config.id, {
        openId,
        content: recognition || '[语音]', // 如果有语音识别结果就显示，否则显示[语音]
        messageType: 'voice',
        mediaId,
        msgId,
        createTime: parseInt(createTime) * 1000, // 转换为毫秒时间戳
        format // 语音格式
      });

      console.log('语音消息已保存到数据库');
      return 'success';
    } catch (error) {
      console.error('处理语音消息失败:', error);
      return 'success';
    }
  }

  private async handleEventMessage(message: any, config: any) {
    try {
      // 处理事件消息
      console.log('收到事件消息:', message);

      const openId = Array.isArray(message.FromUserName) ? message.FromUserName[0] : message.FromUserName;
      const event = Array.isArray(message.Event) ? message.Event[0] : message.Event;
      const createTime = Array.isArray(message.CreateTime) ? message.CreateTime[0] : message.CreateTime;

      // 只保存某些重要的事件消息，比如关注、取消关注等
      if (event === 'subscribe' || event === 'unsubscribe') {
        const content = event === 'subscribe' ? '[用户关注]' : '[用户取消关注]';

        await this.wxServiceService.receiveMessage(config.id, {
          openId,
          content,
          messageType: 'event',
          createTime: parseInt(createTime) * 1000 // 转换为毫秒时间戳
        });

        console.log(`事件消息已保存到数据库: ${event}`);
      }

      return 'success';
    } catch (error) {
      console.error('处理事件消息失败:', error);
      return 'success';
    }
  }

  // 新增：构建 XML 响应
  private buildXMLResponse(content: string): string {
    if (content === 'success') {
      return 'success'; // 如果是 success，直接返回字符串
    }
    
    // 其他情况返回 XML 格式
    return `<xml>
      <return_code><![CDATA[SUCCESS]]></return_code>
      <return_msg><![CDATA[${content}]]></return_msg>
    </xml>`;
  }

  // 新增：构建加密后的回复
  private async buildEncryptedReply(config: any, content: string, nonce: string): Promise<string> {
    try {
      // 1. 构造回复消息
      const replyMsg = `{"demo_resp":"good luck"}`;

      // 2. 加密回复消息
      const { Encrypt, MsgSignature, TimeStamp } = this.encryptReplyMessage(config, replyMsg, nonce);

      // 3. 构建 XML 响应
      const xmlResponse = `<xml>
        <Encrypt><![CDATA[${Encrypt}]]></Encrypt>
        <MsgSignature><![CDATA[${MsgSignature}]]></MsgSignature>
        <TimeStamp>${TimeStamp}</TimeStamp>
        <Nonce><![CDATA[${nonce}]]></Nonce>
      </xml>`;

      // console.log('构造的加密回复:', xmlResponse);
      return xmlResponse;
    } catch (error) {
      console.error('构建加密回复失败:', error);
      return this.buildXMLResponse('success');
    }
  }

  // 新增：加密回复消息
  private encryptReplyMessage(config: any, replyMsg: string, nonce: string): any {
    // 1. 生成 16 字节的随机字符串
    const randomStr = crypto.randomBytes(16).toString('hex').slice(0, 16);

    // 2. 获取 AppId
    const appId = config.appId;

    // 3. 构造 FullStr
    const msgLen = Buffer.byteLength(replyMsg, 'utf8');
    const msgLenBuffer = Buffer.alloc(4);
    msgLenBuffer.writeUInt32BE(msgLen, 0);
    const fullStr = randomStr + msgLenBuffer.toString('binary') + replyMsg + appId;

    // 4. AES 加密
    const encodingAESKey = config.encodingAesKey;
    const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
    const iv = aesKey.slice(0, 16);
    const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
    cipher.setAutoPadding(false);
    let encrypted = cipher.update(this.pkcs7Encode(fullStr), 'binary', 'binary');
    encrypted += cipher.final('binary');
    const Encrypt = Buffer.from(encrypted, 'binary').toString('base64');

    // 5. 生成 MsgSignature
    const TimeStamp = Math.floor(Date.now() / 1000);
    const signatureArr = [config.token, String(TimeStamp), nonce, Encrypt];
    signatureArr.sort();
    const signatureStr = signatureArr.join('');
    const MsgSignature = crypto.createHash('sha1').update(signatureStr, 'utf8').digest('hex');

    return { Encrypt, MsgSignature, TimeStamp };
  }

  // 新增：PKCS7 填充
  private pkcs7Encode(text: string): Buffer {
    const blockSize = 32;
    const textLength = Buffer.byteLength(text, 'utf8');
    const amountToPad = blockSize - (textLength % blockSize);
    const pad = Buffer.alloc(amountToPad, amountToPad);
    const result = Buffer.concat([Buffer.from(text, 'utf8'), pad]);
    return result;
  }
}
