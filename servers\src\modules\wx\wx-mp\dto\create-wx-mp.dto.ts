import { IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsUrl, Matches, IsBoolean } from 'class-validator';

export class CreateWxMpDto {
  @IsString()
  name: string;

  @IsString()
  @Matches(/^[A-Za-z0-9]$/, { message: 'serviceType must be a single alphanumeric character' })
  serviceType: string;

  @IsOptional()
  @IsString()
  auditStatus?: string;

  @IsString()
  appId: string;

  @IsOptional()
  @IsString()
  appSecret?: string;

  @IsOptional()
  @IsString()
  token?: string;

  @IsOptional()
  @IsString()
  encodingAesKey?: string;

  @IsOptional()
  @IsString()
  welcomeMessage?: string;

  @IsOptional()
  @IsBoolean()
  strikethroughEnabled?: boolean;

  @IsOptional()
  @IsString()
  remark?: string;
}