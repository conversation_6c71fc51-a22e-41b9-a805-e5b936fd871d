import { Controller, Get, Put, Post, Body, Delete, Param, Query, UseGuards, UseInterceptors, ExecutionContext } from '@nestjs/common';
import { User, UserEnum } from 'src/common/decorators/user.decorator';
import { CreateWxMpDto } from './dto/create-wx-mp.dto';
import { UpdateWxMpDto } from './dto/update-wx-mp.dto';
import { QueryWxMpDto } from './dto/query-wx-mp.dto';
import { AllocateWxMpDto } from './dto/allocate-wx-mp.dto';
import { GenerateQrcodeDto } from './dto/generate-qrcode.dto';
import { UpdateWelcomeMessageDto } from './dto/welcome-message.dto';
import { UpdateStrikethroughSettingDto } from './dto/strikethrough-setting.dto';
import { AuthGuard } from '@nestjs/passport';
import { WxMpService } from './wx-mp.service';
import { RequiresPermissions } from 'src/common/decorators/requires-permissions.decorator';
import { DataObj } from 'src/common/class/data-obj.class';

@Controller('wx/mp')
@UseGuards(AuthGuard('jwt'))
export class WxMpController {
  constructor(private readonly wxMpService: WxMpService) { }

  @Post()
  @RequiresPermissions('wx:mp:add')
  async create(@Body() createWxMpDto: CreateWxMpDto, @User(UserEnum.userName) userName: string) {
    return await this.wxMpService.create(createWxMpDto, userName);
  }

  @Get('list')
  @RequiresPermissions('wx:mp:list')
  async findAll(@Query() query: QueryWxMpDto) {
    return await this.wxMpService.findAll(query);
  }

  @Get(':id')
  @RequiresPermissions('wx:mp:query')
  async findOne(@Param('id') id: string) {
    const mp = await this.wxMpService.findOne(+id);
    return DataObj.create(mp);
  }

  @Put(':id')
  @RequiresPermissions('wx:mp:update')
  async update(@Param('id') id: string, @Body() updateWxMpDto: UpdateWxMpDto) {
    return await this.wxMpService.update(+id, updateWxMpDto);
  }

  @Delete(':ids')
  @RequiresPermissions('wx:mp:delete')
  async remove(@Param('ids') ids: string) {
    return await this.wxMpService.remove(ids.split(',').map(id => +id));
  }

  @Post(':id/allocate')
  @RequiresPermissions('wx:mp:allocate')
  async allocate(@Param('id') id: string, @Body() allocateWxMpDto: AllocateWxMpDto) {
    const { userId } = allocateWxMpDto;
    const result = await this.wxMpService.allocate(+id, userId);
    return DataObj.create(result);
  }

  @Get(':id/check')
  async check(@Param('id') id: string) {
    return await this.wxMpService.check(+id);
  }

  @Post(':id/generate-qrcode')
  @RequiresPermissions('wx:mp:update')
  async generateQrcode(@Param('id') id: string, @Body() generateQrcodeDto: GenerateQrcodeDto) {
    return await this.wxMpService.generateQrcode(+id, generateQrcodeDto.path, generateQrcodeDto.width);
  }

  @Get(':id/welcome-message')
  // @RequiresPermissions('wx:mp:query') // 临时注释，待配置权限后启用
  async getWelcomeMessage(@Param('id') id: string) {
    const result = await this.wxMpService.getWelcomeMessage(+id);
    return DataObj.create(result);
  }

  @Put(':id/welcome-message')
  // @RequiresPermissions('wx:mp:update') // 临时注释，待配置权限后启用
  async updateWelcomeMessage(
    @Param('id') id: string,
    @Body() updateWelcomeMessageDto: UpdateWelcomeMessageDto,
    @User(UserEnum.userName) userName: string
  ) {
    console.log(`控制器收到更新欢迎语请求 - ID: ${id}, DTO:`, updateWelcomeMessageDto, `用户: ${userName}`);
    const result = await this.wxMpService.updateWelcomeMessage(+id, updateWelcomeMessageDto.welcomeMessage, userName);
    console.log('控制器返回结果:', result);
    return DataObj.create(result);
  }

  @Get(':id/strikethrough-setting')
  // @RequiresPermissions('wx:mp:query') // 临时注释，待配置权限后启用
  async getStrikethroughSetting(@Param('id') id: string) {
    const result = await this.wxMpService.getStrikethroughSetting(+id);
    return DataObj.create(result);
  }

  @Put(':id/strikethrough-setting')
  // @RequiresPermissions('wx:mp:update') // 临时注释，待配置权限后启用
  async updateStrikethroughSetting(
    @Param('id') id: string,
    @Body() updateStrikethroughSettingDto: UpdateStrikethroughSettingDto,
    @User(UserEnum.userName) userName: string
  ) {
    console.log(`控制器收到更新删除线设置请求 - ID: ${id}, DTO:`, updateStrikethroughSettingDto, `用户: ${userName}`);
    const result = await this.wxMpService.updateStrikethroughSetting(+id, updateStrikethroughSettingDto.strikethroughEnabled, userName);
    console.log('控制器返回结果:', result);
    return DataObj.create(result);
  }
}