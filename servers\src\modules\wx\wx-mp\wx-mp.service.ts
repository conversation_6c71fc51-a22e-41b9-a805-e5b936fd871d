import { Injectable } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import { SharedService } from '../../../shared/shared.service';
import { CosService } from '../../../shared/cos.service';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

@Injectable()
export class WxMpService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly sharedService: SharedService,
    private readonly cosService: CosService,
  ) { }

  // 创建微信配置
  async create(createWxMpDto: any, userName: string) {
    const domain = this.configService.get<string>('domain');
    // 自动生成Token 32位 必须为英文或数字，长度为3-32字符
    const token = this.sharedService.generateRandomValue(32);
    // EncodingAESKey 43位字符范围为A-Z,a-z,0-9
    const encodingAesKey = this.sharedService.generateRandomValue(43);

    const newConfig = await this.prisma.wxMpConfig.create({
      data: {
        ...createWxMpDto,
        token,
        encodingAesKey,
        // 自动生成服务器URL
        serverUrl: `${domain}/wx/wx-api/${this.sharedService.generateRandomValue(16)}`,
        username: userName,
        createBy: userName,
      }
    });

    // 异步生成小程序码，不阻塞响应
    this.generateQrcodeAsync(newConfig.id).catch(error => {
      console.error(`异步生成小程序码失败 (ID: ${newConfig.id}):`, error.message);
    });

    return newConfig;
  }

  // 查询微信配置列表
  async findAll(query: any) {
    const { pageNum = 1, pageSize = 10, name, username, ...params } = query;
    const skip = (pageNum - 1) * pageSize;

    // 构建查询条件
    const where = {
      ...params,
      ...(name && { name: { contains: name } }),
      ...(username && { username: { contains: username } })
    };

    const [list, total] = await Promise.all([
      this.prisma.wxMpConfig.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createTime: 'desc' }
      }),
      this.prisma.wxMpConfig.count({
        where
      })
    ]);

    return {
      list,
      total
    };
  }

  // 查询单个微信配置
  async findOne(id: number) {
    return await this.prisma.wxMpConfig.findUnique({
      where: { id }
    });
  }

  // 更新微信配置
  async update(id: number, updateWxMpDto: any) {
    return await this.prisma.wxMpConfig.update({
      where: { id },
      data: updateWxMpDto
    });
  }

  // 删除微信配置
  async remove(id: number | number[]) {
    if (Array.isArray(id)) {
      return await this.prisma.wxMpConfig.deleteMany({
        where: { id: { in: id } }
      });
    }
    return await this.prisma.wxMpConfig.delete({
      where: { id }
    });
  }

  // 分配微信配置
  async allocate(id: number, userId: number) {
    // 检查用户是否存在
    const user = await this.prisma.sysUser.findFirst({
      where: {
        userId,
        delFlag: '0',
        status: '0'
      }
    });

    if (!user) {
      throw new Error('指定的用户不存在或已被禁用');
    }

    // 更新配置的服务用户，同时存储用户ID和用户名
    const updatedConfig = await this.prisma.wxMpConfig.update({
      where: { id },
      data: {
        serviceUserId: userId,
        serviceUser: user.userName,
        updateTime: new Date()
      }
    });

    return {
      ...updatedConfig,
      message: `成功分配给用户: ${user.nickName || user.userName}`
    };
  }

  // 检查微信配置连接
  async check(id: number) {
    const config = await this.prisma.wxMpConfig.findUnique({
      where: { id }
    });

    if (!config) {
      throw new Error('配置不存在');
    }

    // 这里应该实现微信服务器连接检查逻辑
    // 简化示例：模拟检查结果
    const isConnected = Math.random() > 0.2;

    return {
      status: isConnected ? 'success' : 'error',
      message: isConnected ? '连接成功' : '连接失败，请检查配置'
    };
  }

  // 异步生成小程序码（用于新增小程序后自动生成）
  private async generateQrcodeAsync(id: number, retryCount: number = 0) {
    const maxRetries = 3;
    const retryDelay = 2000; // 2秒

    try {
      // 等待一小段时间，确保数据库事务完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 检查配置是否存在且有效
      const config = await this.prisma.wxMpConfig.findUnique({
        where: { id }
      });

      if (!config) {
        throw new Error('配置不存在');
      }

      if (!config.appId || !config.appSecret) {
        return;
      }

      const result = await this.generateQrcode(id);

    } catch (error) {
      console.error(`异步生成小程序码失败 (ID: ${id}, 尝试次数: ${retryCount + 1}):`, error.message);

      // 如果还有重试次数，则重试
      if (retryCount < maxRetries) {
        console.log(`将在${retryDelay}ms后重试 (ID: ${id})`);
        setTimeout(() => {
          this.generateQrcodeAsync(id, retryCount + 1).catch(retryError => {
            console.error(`重试失败 (ID: ${id}):`, retryError.message);
          });
        }, retryDelay);
      } else {
        console.error(`小程序码生成最终失败，已达到最大重试次数 (ID: ${id})`);

        // 可以选择在这里记录到数据库或发送通知
        // 例如：更新配置状态为"小程序码生成失败"
        try {
          await this.prisma.wxMpConfig.update({
            where: { id },
            data: {
              // 可以添加一个备注字段来记录错误信息
              // remark: `小程序码生成失败: ${error.message}`
            }
          });
        } catch (updateError) {
          console.error(`更新小程序码状态失败 (ID: ${id}):`, updateError.message);
        }
      }
    }
  }

  // 生成小程序码
  async generateQrcode(id: number, pagePath: string = '/pages/index/intro', width: number = 430) {
    const config = await this.prisma.wxMpConfig.findUnique({
      where: { id }
    });

    if (!config) {
      throw new Error('配置不存在');
    }

    try {
      // 1. 获取access_token
      const accessToken = await this.getAccessToken(config.appId, config.appSecret);
 
      // 2. 调用微信API生成小程序码
      const qrcodeBuffer = await this.generateWxQrcode(accessToken, pagePath, width);

      // 3. 上传小程序码到腾讯云COS
      const fileName = `qrcode_${id}_${Date.now()}.png`;
      const uploadResult = await this.cosService.uploadQrcode(qrcodeBuffer, fileName);

      if (!uploadResult.success) {
        throw new Error(`上传小程序码到COS失败: ${uploadResult.error}`);
      }

      const qrcodeUrl = uploadResult.url;

      // 4. 更新数据库中的qrcode字段
      await this.prisma.wxMpConfig.update({
        where: { id },
        data: { qrcode: qrcodeUrl }
      });

      return {
        status: 'success',
        message: '小程序码生成成功',
        qrcodeUrl,
        cosKey: uploadResult.key
      };
    } catch (error) {
      console.error('生成小程序码失败:', error);
      throw new Error(`生成小程序码失败: ${error.message}`);
    }
  }

  // 获取微信access_token
  private async getAccessToken(appId: string, appSecret: string): Promise<string> {
    try {
      const response = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
        params: {
          grant_type: 'client_credential',
          appid: appId,
          secret: appSecret
        },
        timeout: 10000 // 10秒超时
      });

      // console.log('微信token API响应:', response.data);

      if (response.data.errcode) {
        throw new Error(`获取access_token失败: ${response.data.errmsg} (errcode: ${response.data.errcode})`);
      }

      if (!response.data.access_token) {
        throw new Error('获取access_token失败: 响应中没有access_token字段');
      }

      return response.data.access_token;
    } catch (error) {
      if (error.response) {
        throw new Error(`获取access_token失败: ${error.response.data?.errmsg || error.message}`);
      } else if (error.request) {
        throw new Error('获取access_token失败: 网络请求超时');
      } else {
        throw new Error(`获取access_token失败: ${error.message}`);
      }
    }
  }

  // 调用微信API生成小程序码
  private async generateWxQrcode(accessToken: string, pagePath?: string, width: number = 430): Promise<Buffer> {
    try {
      let apiUrl: string;
      let requestData: any;

      if (pagePath) {
        // 有页面路径时使用 getwxacodeunlimit 接口
        apiUrl = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
        requestData = {
          scene: 'default', // 场景值，最大32个可见字符
          page: pagePath,   // 页面路径
          width,
          auto_color: false,
          line_color: { r: 0, g: 0, b: 0 },
          is_hyaline: false
        };
      } else {
        // 没有页面路径时使用 getwxacode 接口（生成主页小程序码）
        apiUrl = `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`;
        requestData = {
          path: 'pages/index/index', // 默认主页路径
          width,
          auto_color: false,
          line_color: { r: 0, g: 0, b: 0 },
          is_hyaline: false
        };
      }

      // console.log('调用微信API:', apiUrl);
      // console.log('请求参数:', JSON.stringify(requestData, null, 2));

      const response = await axios.post(apiUrl, requestData, {
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30秒超时
      });

      // 检查返回的是否为图片数据
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('image')) {
        // console.log('微信API返回图片数据，大小:', response.data.length, '字节');
        return Buffer.from(response.data);
      } else {
        // 如果不是图片，可能是错误信息
        const errorInfo = JSON.parse(response.data.toString());
        // console.log('微信API返回错误:', errorInfo);
        throw new Error(`微信API错误: ${errorInfo.errmsg || '未知错误'} (errcode: ${errorInfo.errcode})`);
      }
    } catch (error) {
      if (error.response) {
        // 请求已发出，但服务器响应了状态码
        if (error.response.data) {
          try {
            const errorData = JSON.parse(error.response.data.toString());
            throw new Error(`微信API错误: ${errorData.errmsg || '未知错误'} (errcode: ${errorData.errcode})`);
          } catch (parseError) {
            throw new Error(`微信API返回非JSON数据: ${error.response.status}`);
          }
        }
      } else if (error.request) {
        // 请求已发出，但没有收到响应
        throw new Error('微信API请求超时或网络错误');
      } else {
        // 其他错误
        throw new Error(`调用微信API失败: ${error.message}`);
      }
    }
  }

  // 更新欢迎语
  async updateWelcomeMessage(id: number, welcomeMessage: string, userName: string) {
    const result = await this.prisma.wxMpConfig.update({
      where: { id },
      data: {
        welcomeMessage,
        updateBy: userName,
        updateTime: new Date()
      }
    });

    return result;
  }

  // 获取欢迎语
  async getWelcomeMessage(id: number) {
    const config = await this.prisma.wxMpConfig.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        welcomeMessage: true
      }
    });

    if (!config) {
      throw new Error('配置不存在');
    }

    return config;
  }

  // 更新删除线设置
  async updateStrikethroughSetting(id: number, strikethroughEnabled: boolean, userName: string) {
    const result = await this.prisma.wxMpConfig.update({
      where: { id },
      data: {
        strikethroughEnabled,
        updateBy: userName,
        updateTime: new Date()
      }
    });

    return result;
  }

  // 获取删除线设置
  async getStrikethroughSetting(id: number) {
    const config = await this.prisma.wxMpConfig.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        strikethroughEnabled: true
      }
    });

    if (!config) {
      throw new Error('配置不存在');
    }

    return config;
  }
}

