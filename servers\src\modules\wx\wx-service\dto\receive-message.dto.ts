import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class ReceiveMessageDto {
  @IsString()
  @IsNotEmpty()
  openId: string;

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsString()
  @IsOptional()
  messageType?: string = 'text';

  @IsString()
  @IsOptional()
  userName?: string;

  @IsString()
  @IsOptional()
  mediaId?: string;

  @IsString()
  @IsOptional()
  msgId?: string; // 微信消息ID

  @IsOptional()
  createTime?: number; // 微信消息创建时间戳
}
