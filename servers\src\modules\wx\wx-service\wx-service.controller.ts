import { Controller, Get, Post, Body, Param, Query, UseGuards, Put } from '@nestjs/common';
import { User, UserEnum } from 'src/common/decorators/user.decorator';
import { AuthGuard } from '@nestjs/passport';
import { WxServiceService } from './wx-service.service';
import { RequiresPermissions } from 'src/common/decorators/requires-permissions.decorator';
import { DataObj } from 'src/common/class/data-obj.class';
import { SendMessageDto } from './dto/send-message.dto';
import { ReceiveMessageDto } from './dto/receive-message.dto';
import { Public } from 'src/common/decorators/public.decorator';

@Controller('wx/service')
@UseGuards(AuthGuard('jwt'))
export class WxServiceController {
  constructor(private readonly wxServiceService: WxServiceService) {}

  @Get('my-configs')
  // @RequiresPermissions('wx:service:query') // 临时注释，待配置权限后启用
  async getMyConfigs(@Query() query: any, @User(UserEnum.userName) userName: string) {
    const result = await this.wxServiceService.getMyConfigs(query, userName);
    return DataObj.create(result);
  }

  @Get('conversations')
  // @RequiresPermissions('wx:service:query') // 临时注释，待配置权限后启用
  async getConversations(@User(UserEnum.userName) userName: string) {
    const result = await this.wxServiceService.getConversations(userName);
    return DataObj.create(result);
  }

  @Get(':configId/messages')
  // @RequiresPermissions('wx:service:query') // 临时注释，待配置权限后启用
  async getMessages(
    @Param('configId') configId: string,
    @Query('conversationId') conversationId: string,
    @User(UserEnum.userName) userName: string
  ) {
    const result = await this.wxServiceService.getMessages(
      +configId,
      userName,
      conversationId ? +conversationId : undefined
    );
    return DataObj.create(result);
  }

  @Post(':configId/send')
  // @RequiresPermissions('wx:service:send') // 临时注释，待配置权限后启用
  async sendMessage(
    @Param('configId') configId: string,
    @Body() data: SendMessageDto,
    @User(UserEnum.userName) userName: string
  ) {
    const result = await this.wxServiceService.sendMessage(+configId, data, userName);
    return DataObj.create(result);
  }

  @Put('conversations/:conversationId/read')
  // @RequiresPermissions('wx:service:read') // 临时注释，待配置权限后启用
  async markAsRead(
    @Param('conversationId') conversationId: string,
    @User(UserEnum.userName) userName: string
  ) {
    const result = await this.wxServiceService.markAsRead(+conversationId, userName);
    return DataObj.create(result);
  }

}

@Controller('wx/service/public')
export class WxServicePublicController {
  constructor(private readonly wxServiceService: WxServiceService) {}

  @Post(':configId/receive')
  @Public()
  // 这个接口用于接收微信消息，不需要JWT认证，因为是微信服务器调用
  async receiveMessage(
    @Param('configId') configId: string,
    @Body() data: ReceiveMessageDto
  ) {
    const result = await this.wxServiceService.receiveMessage(+configId, data);
    return DataObj.create(result);
  }
}
