import { Injectable } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import { CosService } from '../../../shared/cos.service';
import axios from 'axios';

@Injectable()
export class WxServiceService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly cosService: CosService,
  ) {}

  // 获取我分配的微信配置列表
  async getMyConfigs(query: any, userName: string) {
    const { pageNum = 1, pageSize = 10, name } = query;
    const skip = (pageNum - 1) * pageSize;

    // 构建查询条件
    const where = {
      serviceUser: userName,
      ...(name && { name: { contains: name } })
    };

    const [list, total] = await Promise.all([
      this.prisma.wxMpConfig.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { createTime: 'desc' }
      }),
      this.prisma.wxMpConfig.count({
        where
      })
    ]);

    return {
      list,
      total
    };
  }

  // 获取会话列表
  async getConversations(userName: string) {
    // 获取我分配的配置
    const configs = await this.prisma.wxMpConfig.findMany({
      where: {
        serviceUser: userName
      }
    });

    if (configs.length === 0) {
      return [];
    }

    const configIds = configs.map(config => config.id);

    // 从数据库获取真实的会话数据
    const conversations = await this.prisma.wxConversation.findMany({
      where: {
        configId: {
          in: configIds
        },
        status: '1' // 只获取活跃的会话
      },
      include: {
        config: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        lastMessageTime: 'desc'
      }
    });

    // 转换数据格式以匹配前端期望的格式
    return conversations.map(conv => ({
      id: conv.id,
      userName: conv.userName || conv.openId,
      appName: conv.config.name,
      lastMessage: conv.lastMessage || '',
      lastMessageTime: conv.lastMessageTime,
      unreadCount: conv.unreadCount,
      configId: conv.configId,
      openId: conv.openId
    }));
  }

  // 获取对话记录
  async getMessages(configId: number, userName: string, conversationId?: number) {
    // 首先验证用户是否有权限查看此配置的消息
    const config = await this.prisma.wxMpConfig.findFirst({
      where: {
        id: configId,
        serviceUser: userName
      }
    });

    if (!config) {
      throw new Error('无权限查看此配置的对话记录');
    }

    // 构建查询条件
    const whereCondition: any = {
      configId
    };

    // 如果提供了会话ID，则按会话ID过滤
    if (conversationId) {
      whereCondition.conversationId = conversationId;
    }

    // 从数据库获取真实的消息记录
    const messages = await this.prisma.wxMessage.findMany({
      where: whereCondition,
      orderBy: {
        createTime: 'asc'
      }
    });

    return messages;
  }

  // 发送消息
  async sendMessage(configId: number, data: any, userName: string) {
    const { openId, content, messageType = 'text' } = data;

    // 首先验证用户是否有权限操作此配置
    const config = await this.prisma.wxMpConfig.findFirst({
      where: {
        id: configId,
        serviceUser: userName
      }
    });

    if (!config) {
      throw new Error('无权限操作此配置');
    }

    // 查找或创建会话
    let conversation = await this.prisma.wxConversation.findFirst({
      where: {
        configId,
        openId
      }
    });

    if (!conversation) {
      conversation = await this.prisma.wxConversation.create({
        data: {
          configId,
          openId,
          userName: data.userName || openId,
          status: '1'
        }
      });
    }

    // 保存消息到数据库
    const message = await this.prisma.wxMessage.create({
      data: {
        configId,
        conversationId: conversation.id,
        openId,
        fromUser: false, // 客服发送的消息
        messageType,
        content
      }
    });

    // 更新会话的最后消息信息
    await this.prisma.wxConversation.update({
      where: {
        id: conversation.id
      },
      data: {
        lastMessage: content,
        lastMessageTime: new Date(),
        updateTime: new Date()
      }
    });

    // 调用微信API发送消息
    try {
      let wxContent = content;

      // 如果是图片消息，需要先上传到微信服务器获取media_id
      if (messageType === 'image') {
        console.log('开始上传图片到微信服务器:', content);
        wxContent = await this.uploadImageToWx(config, content);
        console.log('图片上传成功，获得media_id:', wxContent);
      }

      // 如果是语音消息，需要先上传到微信服务器获取media_id
      if (messageType === 'voice') {
        console.log('开始上传语音到微信服务器:', content);
        wxContent = await this.uploadVoiceToWx(config, content);
        console.log('语音上传成功，获得media_id:', wxContent);
      }

      await this.sendWxCustomMessage(config, openId, wxContent, messageType);
      console.log('微信客服消息发送成功');
    } catch (error) {
      console.error('微信客服消息发送失败:', error.message);
      // 即使微信API调用失败，也不影响数据库保存，只记录错误
    }

    return {
      success: true,
      message: '消息发送成功',
      data: message
    };
  }

  // 接收用户消息（由微信API调用）
  async receiveMessage(configId: number, messageData: any) {
    let { openId, content, messageType = 'text', userName, mediaId, format } = messageData;

    // 如果是图片消息，需要下载并保存到我们的服务器
    if (messageType === 'image' && content) {
      try {
        console.log('开始下载用户发送的图片:', content);
        const localImagePath = await this.downloadAndSaveUserImage(content);
        console.log('用户图片下载成功，本地路径:', localImagePath);
        content = localImagePath; // 使用本地路径替换微信URL
      } catch (error) {
        console.error('下载用户图片失败:', error.message);
        // 如果下载失败，仍然保存原始URL，但会有防盗链问题
      }
    }

    // 如果是语音消息，需要下载并保存到我们的服务器
    if (messageType === 'voice' && mediaId) {
      try {
        console.log('开始下载用户发送的语音:', mediaId);
        const localVoicePath = await this.downloadAndSaveUserVoice(configId, mediaId, format);
        console.log('用户语音下载成功，本地路径:', localVoicePath);
        // 保存语音文件URL，但保留原始的语音识别文本作为content显示
        messageData.voiceUrl = localVoicePath;
      } catch (error) {
        console.error('下载用户语音失败:', error.message);
        // 如果下载失败，仍然保存原始内容
      }
    }

    // 查找或创建会话
    let conversation = await this.prisma.wxConversation.findFirst({
      where: {
        configId,
        openId
      }
    });

    const isNewConversation = !conversation;

    if (!conversation) {
      conversation = await this.prisma.wxConversation.create({
        data: {
          configId,
          openId,
          userName: userName || openId,
          status: '1'
        }
      });
    }

    // 保存用户消息到数据库
    const message = await this.prisma.wxMessage.create({
      data: {
        configId,
        conversationId: conversation.id,
        openId,
        fromUser: true, // 用户发送的消息
        messageType,
        content,
        mediaId: messageData.mediaId,
        voiceUrl: messageData.voiceUrl // 语音文件URL
      }
    });

    // 更新会话信息
    await this.prisma.wxConversation.update({
      where: {
        id: conversation.id
      },
      data: {
        lastMessage: content,
        lastMessageTime: new Date(),
        unreadCount: {
          increment: 1 // 增加未读消息数
        },
        updateTime: new Date()
      }
    });

    // 如果是新会话，检查是否需要发送欢迎语
    if (isNewConversation) {
      await this.sendWelcomeMessageIfConfigured(configId, openId, conversation.id);
    }

    return message;
  }

  // 标记消息为已读
  async markAsRead(conversationId: number, userName: string) {
    // 验证权限
    const conversation = await this.prisma.wxConversation.findFirst({
      where: {
        id: conversationId,
        config: {
          serviceUser: userName
        }
      }
    });

    if (!conversation) {
      throw new Error('无权限操作此会话');
    }

    // 重置未读消息数
    await this.prisma.wxConversation.update({
      where: {
        id: conversationId
      },
      data: {
        unreadCount: 0
      }
    });

    return { success: true };
  }

  // 获取微信access_token
  private async getWxAccessToken(appId: string, appSecret: string): Promise<string> {
    try {
      console.log('获取微信access_token, appId:', appId.substring(0, 8) + '...');

      const response = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
        params: {
          grant_type: 'client_credential',
          appid: appId,
          secret: appSecret
        },
        timeout: 10000 // 10秒超时
      });

      console.log('微信token API响应:', response.data);

      if (response.data.errcode) {
        throw new Error(`获取access_token失败: ${response.data.errmsg} (errcode: ${response.data.errcode})`);
      }

      if (!response.data.access_token) {
        throw new Error('获取access_token失败: 响应中没有access_token字段');
      }

      console.log('获取access_token成功');
      return response.data.access_token;
    } catch (error) {
      if (error.response) {
        console.log('微信token API错误响应:', error.response.data);
        throw new Error(`获取access_token失败: ${error.response.data?.errmsg || error.message}`);
      } else if (error.request) {
        throw new Error('获取access_token失败: 网络请求超时');
      } else {
        throw new Error(`获取access_token失败: ${error.message}`);
      }
    }
  }

  // 发送微信客服消息
  private async sendWxCustomMessage(config: any, openId: string, content: string, messageType: string = 'text'): Promise<void> {
    try {
      // 1. 获取access_token
      const accessToken = await this.getWxAccessToken(config.appId, config.appSecret);

      // 2. 构造消息数据
      let messageData: any = {
        touser: openId,
        msgtype: messageType
      };

      // 根据消息类型构造不同的消息体
      switch (messageType) {
        case 'text':
          messageData.text = {
            content: content
          };
          break;
        case 'image':
          messageData.image = {
            media_id: content // 对于图片消息，content应该是media_id
          };
          break;
        case 'voice':
          messageData.voice = {
            media_id: content // 对于语音消息，content应该是media_id
          };
          break;
        default:
          messageData.text = {
            content: content
          };
      }

      // 3. 调用微信客服消息API
      const apiUrl = `https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=${accessToken}`;

      console.log('调用微信客服消息API:', apiUrl);
      console.log('消息数据:', JSON.stringify(messageData, null, 2));

      const response = await axios.post(apiUrl, messageData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10秒超时
      });

      console.log('微信客服消息API响应:', response.data);

      if (response.data.errcode && response.data.errcode !== 0) {
        throw new Error(`发送客服消息失败: ${response.data.errmsg} (errcode: ${response.data.errcode})`);
      }

      console.log('微信客服消息发送成功');
    } catch (error) {
      if (error.response) {
        console.log('微信客服消息API错误响应:', error.response.data);
        throw new Error(`发送客服消息失败: ${error.response.data?.errmsg || error.message}`);
      } else if (error.request) {
        throw new Error('发送客服消息失败: 网络请求超时');
      } else {
        throw new Error(`发送客服消息失败: ${error.message}`);
      }
    }
  }

  // 上传图片到微信服务器获取media_id
  private async uploadImageToWx(config: any, imageUrl: string): Promise<string> {
    try {
      // 1. 获取access_token
      const accessToken = await this.getWxAccessToken(config.appId, config.appSecret);

      // 2. 下载图片文件
      console.log('开始下载图片:', imageUrl);

      // 构建完整的图片URL
      let fullImageUrl = imageUrl;
      if (!imageUrl.startsWith('http')) {
        // 如果不是完整URL，构建完整URL
        const domain = this.configService.get('domain') || 'http://localhost:3000';

        // 确保路径以 / 开头
        let cleanPath = imageUrl;
        if (!cleanPath.startsWith('/')) {
          cleanPath = '/' + cleanPath;
        }

        fullImageUrl = domain + cleanPath;
      }

      console.log('完整图片URL:', fullImageUrl);

      const imageResponse = await axios.get(fullImageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000 // 30秒超时
      });

      // 3. 上传到微信服务器
      const uploadUrl = `https://api.weixin.qq.com/cgi-bin/media/upload?access_token=${accessToken}&type=image`;

      console.log('开始上传图片到微信服务器:', uploadUrl);

      const FormData = require('form-data');
      const formData = new FormData();

      // 从URL中提取文件名和扩展名
      const urlParts = imageUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'jpg';

      // 根据扩展名确定MIME类型
      let contentType = 'image/jpeg';
      if (fileExtension === 'png') {
        contentType = 'image/png';
      } else if (fileExtension === 'gif') {
        contentType = 'image/gif';
      }

      formData.append('media', Buffer.from(imageResponse.data), {
        filename: fileName,
        contentType: contentType
      });

      const uploadResponse = await axios.post(uploadUrl, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 30000 // 30秒超时
      });

      console.log('微信图片上传API响应:', uploadResponse.data);

      if (uploadResponse.data.errcode && uploadResponse.data.errcode !== 0) {
        throw new Error(`上传图片到微信失败: ${uploadResponse.data.errmsg} (errcode: ${uploadResponse.data.errcode})`);
      }

      if (!uploadResponse.data.media_id) {
        throw new Error('上传图片到微信失败: 未返回media_id');
      }

      return uploadResponse.data.media_id;
    } catch (error) {
      console.error('上传图片到微信服务器失败:', error.message);
      if (error.response) {
        console.log('微信图片上传API错误响应:', error.response.data);
        throw new Error(`上传图片到微信失败: ${error.response.data?.errmsg || error.message}`);
      } else if (error.request) {
        throw new Error('上传图片到微信失败: 网络请求超时');
      } else {
        throw new Error(`上传图片到微信失败: ${error.message}`);
      }
    }
  }

  // 上传语音到微信服务器获取media_id
  private async uploadVoiceToWx(config: any, voiceUrl: string): Promise<string> {
    try {
      // 1. 获取access_token
      const accessToken = await this.getWxAccessToken(config.appId, config.appSecret);

      // 2. 下载语音文件
      console.log('开始下载语音文件:', voiceUrl);

      // 构建完整的语音URL
      let fullVoiceUrl = voiceUrl;
      if (!voiceUrl.startsWith('http')) {
        // 如果不是完整URL，构建完整URL
        const domain = this.configService.get('domain') || 'http://localhost:3000';

        // 确保路径以 / 开头
        let cleanPath = voiceUrl;
        if (!cleanPath.startsWith('/')) {
          cleanPath = '/' + cleanPath;
        }

        fullVoiceUrl = domain + cleanPath;
      }

      console.log('完整语音URL:', fullVoiceUrl);

      const voiceResponse = await axios.get(fullVoiceUrl, {
        responseType: 'arraybuffer',
        timeout: 30000 // 30秒超时
      });

      // 3. 上传到微信服务器
      const uploadUrl = `https://api.weixin.qq.com/cgi-bin/media/upload?access_token=${accessToken}&type=voice`;

      console.log('开始上传语音到微信服务器:', uploadUrl);

      const FormData = require('form-data');
      const formData = new FormData();

      // 从URL中提取文件名和扩展名
      const urlParts = voiceUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'amr';

      // 根据扩展名确定MIME类型
      let contentType = 'audio/amr';
      if (fileExtension === 'mp3') {
        contentType = 'audio/mp3';
      } else if (fileExtension === 'wav') {
        contentType = 'audio/wav';
      } else if (fileExtension === 'speex') {
        contentType = 'audio/speex';
      }

      formData.append('media', Buffer.from(voiceResponse.data), {
        filename: fileName,
        contentType: contentType
      });

      const uploadResponse = await axios.post(uploadUrl, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 30000 // 30秒超时
      });

      console.log('微信语音上传API响应:', uploadResponse.data);

      if (uploadResponse.data.errcode && uploadResponse.data.errcode !== 0) {
        throw new Error(`上传语音到微信失败: ${uploadResponse.data.errmsg} (errcode: ${uploadResponse.data.errcode})`);
      }

      if (!uploadResponse.data.media_id) {
        throw new Error('上传语音到微信失败: 未返回media_id');
      }

      return uploadResponse.data.media_id;
    } catch (error) {
      console.error('上传语音到微信服务器失败:', error.message);
      if (error.response) {
        console.log('微信语音上传API错误响应:', error.response.data);
        throw new Error(`上传语音到微信失败: ${error.response.data?.errmsg || error.message}`);
      } else if (error.request) {
        throw new Error('上传语音到微信失败: 网络请求超时');
      } else {
        throw new Error(`上传语音到微信失败: ${error.message}`);
      }
    }
  }

  // 下载并保存用户发送的图片
  private async downloadAndSaveUserImage(imageUrl: string): Promise<string> {
    try {
      const dayjs = require('dayjs');

      // 下载图片
      console.log('开始下载用户图片:', imageUrl);
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // 生成文件名
      const timestamp = Date.now();
      const randomNum = Math.round(Math.random() * 1e9);
      const fileName = `${timestamp}-${randomNum}-user-image.jpg`;

      // 上传到腾讯云COS
      const uploadResult = await this.cosService.uploadImage(
        Buffer.from(response.data),
        fileName
      );

      if (!uploadResult.success) {
        throw new Error(`上传图片到COS失败: ${uploadResult.error}`);
      }

      console.log('用户图片上传到COS成功:', uploadResult.url);
      return uploadResult.url;
    } catch (error) {
      console.error('下载用户图片失败:', error.message);
      throw new Error(`下载用户图片失败: ${error.message}`);
    }
  }

  // 下载并保存用户发送的语音
  private async downloadAndSaveUserVoice(configId: number, mediaId: string, format: string = 'amr'): Promise<string> {
    try {
      // 获取配置信息
      const config = await this.prisma.wxMpConfig.findUnique({
        where: { id: configId }
      });

      if (!config) {
        throw new Error('配置不存在');
      }

      // 1. 获取access_token
      const accessToken = await this.getWxAccessToken(config.appId, config.appSecret);

      // 2. 下载语音文件
      console.log('开始下载用户语音:', mediaId);
      const downloadUrl = `https://api.weixin.qq.com/cgi-bin/media/get?access_token=${accessToken}&media_id=${mediaId}`;

      const response = await axios.get(downloadUrl, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // 检查响应是否为语音文件
      const contentType = response.headers['content-type'];
      if (!contentType || (!contentType.includes('audio') && !contentType.includes('application/octet-stream'))) {
        // 可能是错误响应，尝试解析JSON
        try {
          const errorInfo = JSON.parse(response.data.toString());
          throw new Error(`下载语音失败: ${errorInfo.errmsg || '未知错误'} (errcode: ${errorInfo.errcode})`);
        } catch (parseError) {
          throw new Error('下载语音失败: 响应格式不正确');
        }
      }

      // 生成文件名
      const timestamp = Date.now();
      const randomNum = Math.round(Math.random() * 1e9);
      const fileName = `${timestamp}-${randomNum}-user-voice.${format}`;

      // 上传到腾讯云COS
      const uploadResult = await this.cosService.uploadVoice(
        Buffer.from(response.data),
        fileName
      );

      if (!uploadResult.success) {
        throw new Error(`上传语音到COS失败: ${uploadResult.error}`);
      }

      console.log('用户语音上传到COS成功:', uploadResult.url);
      return uploadResult.url;
    } catch (error) {
      console.error('下载用户语音失败:', error.message);
      throw new Error(`下载用户语音失败: ${error.message}`);
    }
  }

  // 发送欢迎语（如果配置了的话）
  private async sendWelcomeMessageIfConfigured(configId: number, openId: string, conversationId: number) {
    try {
      // 获取配置信息，包括欢迎语
      const config = await this.prisma.wxMpConfig.findUnique({
        where: { id: configId },
        select: {
          id: true,
          name: true,
          welcomeMessage: true,
          appId: true,
          appSecret: true
        }
      });

      if (!config || !config.welcomeMessage || config.welcomeMessage.trim() === '') {
        console.log('配置未设置欢迎语，跳过自动发送');
        return;
      }

      console.log(`配置 ${config.name} 设置了欢迎语，准备自动发送:`, config.welcomeMessage);

      // 保存欢迎语消息到数据库
      const welcomeMessage = await this.prisma.wxMessage.create({
        data: {
          configId,
          conversationId,
          openId,
          fromUser: false, // 系统自动发送的消息
          messageType: 'text',
          content: config.welcomeMessage
        }
      });

      // 更新会话的最后消息信息
      await this.prisma.wxConversation.update({
        where: {
          id: conversationId
        },
        data: {
          lastMessage: config.welcomeMessage,
          lastMessageTime: new Date(),
          updateTime: new Date()
        }
      });

      // 调用微信API发送欢迎语
      try {
        await this.sendWxCustomMessage(config, openId, config.welcomeMessage, 'text');
        console.log('欢迎语发送成功');
      } catch (error) {
        console.error('欢迎语微信API发送失败:', error.message);
        // 即使微信API调用失败，也不影响数据库保存，只记录错误
      }

      return welcomeMessage;
    } catch (error) {
      console.error('发送欢迎语失败:', error.message);
      // 欢迎语发送失败不应该影响主流程
    }
  }
}
