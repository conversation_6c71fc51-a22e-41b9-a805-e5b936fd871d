import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import COS = require('cos-nodejs-sdk-v5');
import * as path from 'path';
import dayjs = require('dayjs');

@Injectable()
export class CosService {
  private cos: COS;
  private bucket: string;
  private region: string;
  private domain: string;

  constructor(private readonly configService: ConfigService) {
    const cosConfig = this.configService.get('cos');
    
    this.cos = new COS({
      SecretId: cosConfig.secretId,
      SecretKey: cosConfig.secretKey,
    });
    
    this.bucket = cosConfig.bucket;
    this.region = cosConfig.region;
    this.domain = cosConfig.domain;
  }

  /**
   * 上传文件到腾讯云COS
   * @param fileBuffer 文件缓冲区
   * @param fileName 文件名
   * @param folder 文件夹路径，默认为当前日期
   * @returns 上传结果，包含文件URL
   */
  async uploadFile(fileBuffer: Buffer, fileName: string, folder?: string): Promise<{
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }> {
    try {
      // 如果没有指定文件夹，使用当前日期作为文件夹
      const folderPath = folder || dayjs().format('YYYY-MM-DD');
      
      // 生成唯一文件名
      const timestamp = Date.now();
      const randomNum = Math.round(Math.random() * 1e9);
      const fileExtension = path.extname(fileName);
      const baseName = path.basename(fileName, fileExtension);
      const uniqueFileName = `${timestamp}-${randomNum}-${baseName}${fileExtension}`;
      
      // 构建COS中的文件路径
      const key = `${folderPath}/${uniqueFileName}`;

      // 上传文件到COS
      const result = await new Promise<COS.PutObjectResult>((resolve, reject) => {
        this.cos.putObject({
          Bucket: this.bucket,
          Region: this.region,
          Key: key,
          Body: fileBuffer,
          ContentLength: fileBuffer.length,
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      // 构建访问URL
      const fileUrl = `${this.domain}/${key}`;

      console.log('文件上传到COS成功:', {
        key,
        url: fileUrl,
        location: result.Location
      });

      return {
        success: true,
        url: fileUrl,
        key: key
      };
    } catch (error) {
      console.error('上传文件到COS失败:', error);
      return {
        success: false,
        error: error.message || '上传失败'
      };
    }
  }

  /**
   * 上传图片文件到COS
   * @param fileBuffer 图片文件缓冲区
   * @param originalName 原始文件名
   * @param folder 文件夹路径，默认为 images/当前日期
   * @returns 上传结果
   */
  async uploadImage(fileBuffer: Buffer, originalName: string, folder?: string): Promise<{
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }> {
    const imageFolder = folder || `images/${dayjs().format('YYYY-MM-DD')}`;
    return this.uploadFile(fileBuffer, originalName, imageFolder);
  }

  /**
   * 上传小程序码到COS
   * @param fileBuffer 小程序码文件缓冲区
   * @param fileName 文件名
   * @returns 上传结果
   */
  async uploadQrcode(fileBuffer: Buffer, fileName: string): Promise<{
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }> {
    return this.uploadFile(fileBuffer, fileName, 'qrcode');
  }

  /**
   * 上传头像到COS
   * @param fileBuffer 头像文件缓冲区
   * @param originalName 原始文件名
   * @returns 上传结果
   */
  async uploadAvatar(fileBuffer: Buffer, originalName: string): Promise<{
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }> {
    return this.uploadFile(fileBuffer, originalName, 'avatars');
  }

  /**
   * 上传语音文件到COS
   * @param fileBuffer 语音文件缓冲区
   * @param originalName 原始文件名
   * @param folder 文件夹路径，默认为 voices/当前日期
   * @returns 上传结果
   */
  async uploadVoice(fileBuffer: Buffer, originalName: string, folder?: string): Promise<{
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }> {
    const voiceFolder = folder || `voices/${dayjs().format('YYYY-MM-DD')}`;
    return this.uploadFile(fileBuffer, originalName, voiceFolder);
  }

  /**
   * 删除COS中的文件
   * @param key 文件在COS中的key
   * @returns 删除结果
   */
  async deleteFile(key: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await new Promise<void>((resolve, reject) => {
        this.cos.deleteObject({
          Bucket: this.bucket,
          Region: this.region,
          Key: key,
        }, (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });

      console.log('从COS删除文件成功:', key);
      return { success: true };
    } catch (error) {
      console.error('从COS删除文件失败:', error);
      return {
        success: false,
        error: error.message || '删除失败'
      };
    }
  }

  /**
   * 检查文件是否存在
   * @param key 文件在COS中的key
   * @returns 文件是否存在
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      await new Promise<COS.HeadObjectResult>((resolve, reject) => {
        this.cos.headObject({
          Bucket: this.bucket,
          Region: this.region,
          Key: key,
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 从URL中提取COS的key
   * @param url COS文件URL
   * @returns 文件key，如果不是COS URL则返回null
   */
  extractKeyFromUrl(url: string): string | null {
    try {
      if (url.startsWith(this.domain)) {
        return url.replace(this.domain + '/', '');
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
