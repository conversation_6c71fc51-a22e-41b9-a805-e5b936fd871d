import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CosService } from '../shared/cos.service';
import config from '../config';

describe('CosService', () => {
  let service: CosService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [config],
          cache: true,
          isGlobal: true,
        }),
      ],
      providers: [CosService],
    }).compile();

    service = module.get<CosService>(CosService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should extract key from COS URL', () => {
    const cosUrl = 'https://mp-**********.cos.ap-chongqing.myqcloud.com/images/2024-01-01/test.jpg';
    const key = service.extractKeyFromUrl(cosUrl);
    
    expect(key).toBe('images/2024-01-01/test.jpg');
  });

  it('should return null for non-COS URL', () => {
    const nonCosUrl = 'https://example.com/test.jpg';
    const key = service.extractKeyFromUrl(nonCosUrl);

    expect(key).toBeNull();
  });

  it('should have all required upload methods', () => {
    expect(typeof service.uploadFile).toBe('function');
    expect(typeof service.uploadImage).toBe('function');
    expect(typeof service.uploadQrcode).toBe('function');
    expect(typeof service.uploadAvatar).toBe('function');
    expect(typeof service.uploadVoice).toBe('function');
    expect(typeof service.deleteFile).toBe('function');
    expect(typeof service.fileExists).toBe('function');
  });

  // 注释掉实际上传测试，避免在CI/CD中产生费用
  /*
  it('should upload a test image to COS', async () => {
    const testImageBuffer = Buffer.from('test image data');
    const fileName = 'test-image.jpg';

    const result = await service.uploadImage(testImageBuffer, fileName);

    expect(result.success).toBe(true);
    expect(result.url).toBeDefined();
    expect(result.key).toBeDefined();
    expect(result.url).toContain('cos.ap-chongqing.myqcloud.com');

    console.log('上传结果:', result);
  }, 30000);
  */
});
