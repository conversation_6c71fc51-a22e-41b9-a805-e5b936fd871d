import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { CosService } from '../shared/cos.service';
import { WxMpService } from '../modules/wx/wx-mp/wx-mp.service';
import { WxServiceService } from '../modules/wx/wx-service/wx-service.service';
import { SysUserController } from '../modules/sys/sys-user/sys-user.controller';
import config from '../config';

describe('COS Integration Test', () => {
  let cosService: CosService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [config],
          cache: true,
          isGlobal: true,
        }),
      ],
      providers: [CosService],
    }).compile();

    cosService = module.get<CosService>(CosService);
  });

  it('should create CosService', () => {
    expect(cosService).toBeDefined();
  });

  it('should have correct COS configuration', () => {
    // 测试COS配置是否正确加载
    expect(cosService).toHaveProperty('cos');
    expect(cosService).toHaveProperty('bucket');
    expect(cosService).toHaveProperty('region');
    expect(cosService).toHaveProperty('domain');
  });

  it('should extract key from COS URL correctly', () => {
    const testUrl = 'https://mp-**********.cos.ap-chongqing.myqcloud.com/images/2024-01-01/test.jpg';
    const key = cosService.extractKeyFromUrl(testUrl);
    expect(key).toBe('images/2024-01-01/test.jpg');
  });

  it('should return null for non-COS URL', () => {
    const nonCosUrl = 'https://example.com/test.jpg';
    const key = cosService.extractKeyFromUrl(nonCosUrl);
    expect(key).toBeNull();
  });

  it('should have all required upload methods', () => {
    expect(typeof cosService.uploadFile).toBe('function');
    expect(typeof cosService.uploadImage).toBe('function');
    expect(typeof cosService.uploadQrcode).toBe('function');
    expect(typeof cosService.uploadAvatar).toBe('function');
    expect(typeof cosService.deleteFile).toBe('function');
    expect(typeof cosService.fileExists).toBe('function');
  });
});
