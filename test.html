<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>删除线字体转换器</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }

    .input-section, .output-section {
      margin-bottom: 25px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #555;
    }

    textarea {
      width: 100%;
      min-height: 120px;
      padding: 15px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      font-family: inherit;
      resize: vertical;
      box-sizing: border-box;
    }

    textarea:focus {
      outline: none;
      border-color: #4CAF50;
    }

    .output-text {
      background-color: #f9f9f9;
      border: 2px solid #e0e0e0;
      color: #333;
    }

    .buttons {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      flex-wrap: wrap;
    }

    button {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      transition: background-color 0.3s;
    }

    .convert-btn {
      background-color: #4CAF50;
      color: white;
      flex: 1;
      min-width: 120px;
    }

    .convert-btn:hover {
      background-color: #45a049;
    }

    .copy-btn {
      background-color: #2196F3;
      color: white;
      min-width: 100px;
    }

    .copy-btn:hover {
      background-color: #1976D2;
    }

    .clear-btn {
      background-color: #f44336;
      color: white;
      min-width: 80px;
    }

    .clear-btn:hover {
      background-color: #d32f2f;
    }

    .example {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 8px;
      margin-top: 20px;
      border-left: 4px solid #4CAF50;
    }

    .example h3 {
      margin-top: 0;
      color: #2e7d32;
    }

    .example-text {
      font-size: 18px;
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 4px;
    }

    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      display: none;
      z-index: 1000;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>删除线字体转换器</h1>

    <div class="input-section">
      <label for="inputText">输入文本：</label>
      <textarea id="inputText" placeholder="请输入要转换的文本，支持中文、英文、数字等..."></textarea>
    </div>

    <div class="buttons">
      <button class="convert-btn" onclick="convertText()">转换为删除线字体</button>
      <button class="clear-btn" onclick="clearText()">清空</button>
    </div>

    <div class="output-section">
      <label for="outputText">转换结果：</label>
      <textarea id="outputText" class="output-text" readonly placeholder="转换后的删除线文本将显示在这里..."></textarea>
      <div class="buttons">
        <button class="copy-btn" onclick="copyResult()">复制结果</button>
      </div>
    </div>

    <div class="example">
      <h3>示例效果：</h3>
      <div class="example-text">
        原文：ZX安师大123213<br>
        转换后：<span id="exampleResult">Z̸X̸安̸师̸大̸1̸2̸3̸2̸1̸3̸</span>
      </div>
    </div>
  </div>

  <div id="toast" class="toast">已复制到剪贴板！</div>

  <script>
    // 删除线字符 (Unicode combining character)
    const STRIKETHROUGH_CHAR = '\u0338';

    function convertText() {
      const inputText = document.getElementById('inputText').value;
      if (!inputText.trim()) {
        alert('请输入要转换的文本！');
        return;
      }

      // 为每个字符添加删除线
      const convertedText = inputText.split('').map(char => {
        // 跳过空格和换行符
        if (char === ' ' || char === '\n' || char === '\r') {
          return char;
        }
        return char + STRIKETHROUGH_CHAR;
      }).join('');

      document.getElementById('outputText').value = convertedText;
    }

    function copyResult() {
      const outputText = document.getElementById('outputText');
      if (!outputText.value.trim()) {
        alert('没有可复制的内容！');
        return;
      }

      outputText.select();
      outputText.setSelectionRange(0, 99999); // 移动端兼容

      try {
        document.execCommand('copy');
        showToast();
      } catch (err) {
        // 使用现代 API 作为备选
        if (navigator.clipboard) {
          navigator.clipboard.writeText(outputText.value).then(() => {
            showToast();
          }).catch(() => {
            alert('复制失败，请手动选择文本复制');
          });
        } else {
          alert('复制失败，请手动选择文本复制');
        }
      }
    }

    function clearText() {
      document.getElementById('inputText').value = '';
      document.getElementById('outputText').value = '';
      document.getElementById('inputText').focus();
    }

    function showToast() {
      const toast = document.getElementById('toast');
      toast.style.display = 'block';
      setTimeout(() => {
        toast.style.display = 'none';
      }, 2000);
    }

    // 回车键快速转换
    document.getElementById('inputText').addEventListener('keydown', function(e) {
      if (e.ctrlKey && e.key === 'Enter') {
        convertText();
      }
    });

    // 页面加载时生成示例
    window.onload = function() {
      const exampleText = 'ZX安师大123213';
      const exampleConverted = exampleText.split('').map(char => {
        if (char === ' ') return char;
        return char + STRIKETHROUGH_CHAR;
      }).join('');
      document.getElementById('exampleResult').textContent = exampleConverted;
    };
  </script>
</body>
</html>