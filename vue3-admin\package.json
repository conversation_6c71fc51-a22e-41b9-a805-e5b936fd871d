{"name": "meimei-vue3-ts", "private": true, "version": "0.0.0", "license": "MIT", "keywords": ["electron", "rollup", "vite", "vue3", "vue"], "scripts": {"dev": "vite --mode development", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@handsontable/vue3": "^14.2.0", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "concurrent-tasks": "^1.0.7", "dayjs": "^1.11.10", "echarts": "5.4.0", "element-plus": "^2.6.1", "file-saver": "2.0.5", "fuse.js": "6.6.2", "handsontable": "^14.2.0", "jquery": "^3.7.1", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.0.22", "sortablejs": "^1.15.0", "vue": "^3.4.21", "vue-cropper": "^1.0.9", "vue-plugin-hiprint": "^0.0.54-fix", "vue-router": "4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "4.5.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "3.3.9", "sass": "1.56.1", "typescript": "^5.0.2", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vue-tsc": "0.38.4"}}