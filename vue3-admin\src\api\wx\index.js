import request from '@/utils/request'

// 查询微信配置数据
export function listWxConfig(query) {
  return request({
    url: '/wx/mp/list',
    method: 'get',
    params: query
  })
}

// 查询微信配置详细信息
export function getWxConfig(id) {
  return request({
    url: '/wx/mp/' + id,
    method: 'get'
  })
}

// 修改微信配置信息
export function updateWxConfig(data) {
  return request({
    url: `/wx/mp/${data.id}`,
    method: 'put',
    data: data
  })
}
// 删除微信配置信息
export function delWxConfig(id) { 
  return request({
    url: '/wx/mp/' + id,
    method: 'delete'
  })
}
// 添加微信配置
export function addWxConfig(data) {
  return request({
    url: '/wx/mp',
    method: 'post',
    data
  })
}

// 分配微信配置
export function allocateWxConfig(id, data) {
  return request({
    url: `/wx/mp/${id}/allocate`,
    method: 'post',
    data
  })
}

// 检查微信配置
export function checkWxConfig(data) {
  return request({
    url: '/wx/mp/check',
    method: 'post',
    data
  })
}

// 生成小程序码
export function generateQrcode(id, data) {
  return request({
    url: `/wx/mp/${id}/generate-qrcode`,
    method: 'post',
    data
  })
}

// 获取欢迎语
export function getWelcomeMessage(id) {
  return request({
    url: `/wx/mp/${id}/welcome-message`,
    method: 'get'
  })
}

// 更新删除线设置
export function updateStrikethroughSetting(id, data) {
  return request({
    url: `/wx/mp/${id}/strikethrough-setting`,
    method: 'put',
    data
  })
}

// 获取删除线设置
export function getStrikethroughSetting(id) {
  return request({
    url: `/wx/mp/${id}/strikethrough-setting`,
    method: 'get'
  })
}

// 更新欢迎语
export function updateWelcomeMessage(id, data) {
  return request({
    url: `/wx/mp/${id}/welcome-message`,
    method: 'put',
    data
  })
}
