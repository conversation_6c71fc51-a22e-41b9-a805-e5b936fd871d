import request from '@/utils/request'

// 查询我的微信配置数据
export function listMyWxConfig(query) {
  return request({
    url: '/wx/service/my-configs',
    method: 'get',
    params: query
  })
}

// 获取会话列表
export function getConversations() {
  return request({
    url: '/wx/service/conversations',
    method: 'get'
  })
}

// 查询微信对话记录
export function getWxMessages(configId, conversationId) {
  return request({
    url: `/wx/service/${configId}/messages`,
    method: 'get',
    params: conversationId ? { conversationId } : {}
  })
}

// 发送消息
export function sendWxMessage(configId, data) {
  return request({
    url: `/wx/service/${configId}/send`,
    method: 'post',
    data
  })
}

// 标记会话为已读
export function markConversationAsRead(conversationId) {
  return request({
    url: `/wx/service/conversations/${conversationId}/read`,
    method: 'put'
  })
}
