<template>
  <div class="app-container app-js df fdc">
    <el-form class="top-form" :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入小程序&公众号名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属用户" prop="username">
        <el-input v-model="queryParams.username" placeholder="请输入所属用户" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="客服类别" prop="serviceType">
        <el-select v-model="queryParams.serviceType" placeholder="请选择客服类别" clearable style="width: 200px">
          <el-option v-for="dict in sys_service_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option v-for="dict in sys_onoff_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wx:config:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['wx:config:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['wx:config:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="f1">
      <el-table height="100%" v-loading="loading" :data="wxConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="小程序/公众号名称" align="center" prop="name" :show-overflow-tooltip="true" width="180" />
        <el-table-column label="所属用户" align="center" prop="username" width="120" />
        <el-table-column label="客服人员" align="center" prop="serviceUser" width="120">
          <template #default="scope">
            <span v-if="scope.row.serviceUser">{{ scope.row.serviceUser }}</span>
            <span v-else style="color: #999;">未分配</span>
          </template>
        </el-table-column>
        <el-table-column label="客服类别" align="center" prop="serviceType" width="120">
          <!-- <template #default="scope"> -->
          <!-- <dict-tag :options="sys_service_type" :value="scope.row.serviceType" /> -->
          <!-- </template> -->
        </el-table-column>
        <el-table-column label="累计接待" align="center" prop="total接待" width="100" />
        <el-table-column label="审核状态" align="center" prop="auditStatus" width="120">
          <template #default="scope">
            <!-- <dict-tag :options="sys_audit_status" :value="scope.row.auditStatus" /> -->
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <!-- <template #default="scope"> -->
          <!-- <dict-tag :options="sys_onoff_status" :value="scope.row.status" /> -->
          <!-- </template> -->
        </el-table-column>
        <el-table-column label="小程序码" align="center" width="100">
          <template #default="scope">
            <el-button v-if="scope.row.qrcode" link type="text" plain icon="PictureFilled" @click="previewImg(scope.row.qrcode)"></el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleAllocate(scope.row)" v-hasPermi="['wx:config:allocate']">分配</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['wx:config:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['wx:config:remove']">删除</el-button>
            <el-button link type="primary" @click="handleCheck(scope.row)" v-hasPermi="['wx:config:check']">检查</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" :selection="ids.length" />

    <!-- 分配用户对话框 -->
    <el-dialog title="分配客服人员" v-model="allocateOpen" draggable :close-on-click-modal="false" width="800px" append-to-body>
      <el-form :model="allocateQueryParams" ref="allocateQueryRef" :inline="true">
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="allocateQueryParams.userName"
            placeholder="请输入用户名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleAllocateQuery"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input
            v-model="allocateQueryParams.phonenumber"
            placeholder="请输入手机号码"
            clearable
            style="width: 200px"
            @keyup.enter="handleAllocateQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleAllocateQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetAllocateQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row>
        <el-table @row-click="clickAllocateRow" ref="allocateTableRef" :data="allocateUserList" @selection-change="handleAllocateSelectionChange" height="260px">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="用户名称" prop="userName" :show-overflow-tooltip="true" />
          <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
          <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
          <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="allocateTotal > 0"
          :total="allocateTotal"
          v-model:page="allocateQueryParams.pageNum"
          v-model:limit="allocateQueryParams.pageSize"
          @pagination="getAllocateUserList"
        />
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleConfirmAllocate">确 定</el-button>
          <el-button @click="allocateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" draggable :close-on-click-modal="false" width="780px" append-to-body>
      <el-form ref="wxConfigRef" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="URL(服务器地址)" prop="serverUrl">
              <el-input v-model="form.serverUrl" placeholder="无需输入，自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Token(令牌)" prop="token">
              <el-input v-model="form.token" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="EncodingAESKey" prop="encodingAesKey">
              <el-input v-model="form.encodingAesKey" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="小程序/公众号名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AppID" prop="appId">
              <el-input v-model="form.appId" placeholder="请输入AppID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AppSecret" prop="appSecret">
              <el-input v-model="form.appSecret" placeholder="请输入AppSecret" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="wxConfig">
import { listWxConfig, getWxConfig, delWxConfig, addWxConfig, updateWxConfig, allocateWxConfig, checkWxConfig } from "@/api/wx";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const wxConfigList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 分配相关变量
const allocateOpen = ref(false);
const allocateUserList = ref([]);
const allocateTotal = ref(0);
const allocateLoading = ref(false);
const selectedUserId = ref("");
const currentAllocateConfigId = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 15,
    wxConfigName: undefined,
    createBy: undefined,
    status: undefined,
  },
  allocateQueryParams: {
    pageNum: 1,
    pageSize: 15,
    userName: undefined,
    phonenumber: undefined,
    status: "0", // 只查询正常状态的用户
  },
  rules: {
    name: [{ required: true, message: "小程序&公众号名称不能为空", trigger: "blur" }],
    serviceType: [{ required: true, message: "客服类别不能为空", trigger: "change" }],
    appId: [{ required: true, message: "AppID不能为空", trigger: "blur" }],
    appSecret: [{ required: true, message: "AppSecret不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules, allocateQueryParams } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listWxConfig(queryParams.value).then((response) => {
    wxConfigList.value = response.list;
    total.value = response.total;
    loading.value = false;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  data.form = {
    id: undefined,
    name: undefined,
    serviceType: "1",
    token: "",
    encodingAesKey: "",
    appId: undefined,
    appSecret: undefined,
    serverUrl: undefined,
    qrcode: undefined,
  };
  proxy.resetForm("wxConfigRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

const previewUrl = ref("");
const previewVisible = ref(false);
const previewImg = (url) => {
  if (!url) return;
  window.open(url, "_blank");
};

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增小程序&公众号";
}
/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const wxConfigId = row.id || ids.value;
  getWxConfig(wxConfigId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改小程序&公众号";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["wxConfigRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateWxConfig(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWxConfig(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
// 分配按钮操作
function handleAllocate(row) {
  currentAllocateConfigId.value = row.id;
  allocateQueryParams.value.pageNum = 1;
  getAllocateUserList();
  allocateOpen.value = true;
}

// 获取可分配的用户列表
function getAllocateUserList() {
  allocateLoading.value = true;
  listUser(allocateQueryParams.value).then((response) => {
    allocateUserList.value = response.rows;
    allocateTotal.value = response.total;
    allocateLoading.value = false;
  });
}

// 分配用户搜索
function handleAllocateQuery() {
  allocateQueryParams.value.pageNum = 1;
  getAllocateUserList();
}

// 重置分配用户搜索
function resetAllocateQuery() {
  proxy.resetForm("allocateQueryRef");
  handleAllocateQuery();
}

// 选择分配用户行
function clickAllocateRow(row) {
  proxy.$refs["allocateTableRef"].toggleRowSelection(row);
}

// 分配用户选择变化
function handleAllocateSelectionChange(selection) {
  selectedUserId.value = selection.length > 0 ? selection[0].userId : "";
}

// 确认分配
function handleConfirmAllocate() {
  if (!selectedUserId.value) {
    proxy.$modal.msgError("请选择要分配的用户");
    return;
  }

  const allocateData = {
    userId: selectedUserId.value
  };

  allocateWxConfig(currentAllocateConfigId.value, allocateData).then(() => {
    proxy.$modal.msgSuccess("分配成功");
    allocateOpen.value = false;
    getList();
  }).catch(() => {
    proxy.$modal.msgError("分配失败");
  });
}

// 检查按钮操作
function handleCheck(row) {
  proxy.$modal
    .confirm('是否确认检查配置ID为"' + row.id + '"的数据项？')
    .then(function () {
      return checkWxConfig(row.id);
    })
    .then((response) => {
      proxy.$modal.msgSuccess("检查成功");
      proxy.$modal.info(response.data);
    })
    .catch(() => {});
}

// 删除按钮操作
function handleDelete(row) {
  const configIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除配置ID为"' + configIds + '"的数据项？')
    .then(function () {
      return delWxConfig(configIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
