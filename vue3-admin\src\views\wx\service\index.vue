<template>
  <div class="chat-container">
    <!-- 左侧会话列表 -->
    <div class="chat-sidebar">
      <div class="sidebar-header">
        <div class="header-title">
          <h3>{{ conversations[0]?.appName || "" }}客服</h3>
          <div class="header-actions">
            <el-button size="small" circle icon="Setting" @click="openSettings" title="设置" />
            <el-button size="small" circle icon="Refresh" @click="loadConversations" :loading="loading" title="刷新" />
          </div>
        </div>
        <el-input v-model="searchKeyword" placeholder="搜索会话" size="small" prefix-icon="Search" @input="handleSearch" />
      </div>

      <div class="conversation-list" v-loading="loading">
        <div v-if="filteredConversations.length === 0 && !loading" class="empty-conversations">
          <el-empty description="暂无会话" />
        </div>
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ active: currentConversation?.id === conversation.id }"
          @click="selectConversation(conversation)"
        >
          <div class="avatar">
            <span>{{ conversation.userName.charAt(0).toUpperCase() }}</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <span class="user-name">{{ conversation.userName }}</span>
              <span class="time">{{ formatTime(conversation.lastMessageTime) }}</span>
            </div>
            <div class="last-message">{{ conversation.lastMessage }}</div>
            <div class="app-name">{{ conversation.appName }}</div>
          </div>
          <div v-if="conversation.unreadCount > 0" class="unread-badge">
            {{ conversation.unreadCount }}
          </div>
        </div>
      </div>
    </div>

    <!-- 广告区 -->
    <div class="ad-main">
      <div v-if="latestNotice" class="notice-content">
        <div class="notice-body" v-html="latestNotice.noticeContent"></div>
      </div>
      <div v-else class="no-notice">
        <el-icon class="no-notice-icon"><DocumentRemove /></el-icon>
        <span>暂无通知公告</span>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <div v-if="!currentConversation" class="no-conversation">
        <div class="welcome-message">
          <el-icon size="64" color="#ccc"><ChatDotRound /></el-icon>
          <p>请选择一个会话开始聊天</p>
        </div>
      </div>

      <div v-else class="chat-content">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="user-info">
            <div class="avatar">
              <span>{{ currentConversation.userName.charAt(0).toUpperCase() }}</span>
            </div>
            <div class="info">
              <div class="name">{{ currentConversation.userName }}</div>
              <div class="app">{{ currentConversation.appName }}</div>
            </div>
          </div>
          <div class="chat-actions">
            <el-button size="small" @click="handleEndConversation">结束会话</el-button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div
          class="message-list"
          ref="messageListRef"
          v-loading="messageLoading"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragenter="handleDragEnter"
          @dragleave="handleDragLeave"
          :class="{ 'drag-over': isDragOver }"
        >
          <div v-if="currentMessages.length === 0 && !messageLoading" class="empty-messages">
            <el-empty description="暂无消息记录" />
          </div>
          <div v-for="message in currentMessages" :key="message.id" class="message-wrapper">
            <div class="message-time" v-if="shouldShowTime(message)">
              {{ formatMessageTime(message.createTime) }}
            </div>
            <div class="message-item" :class="{ 'is-self': !message.fromUser }">
              <div class="message-avatar">
                <span>{{ message.fromUser ? currentConversation.userName.charAt(0).toUpperCase() : "客" }}</span>
              </div>
              <div class="message-content">
                <div class="message-bubble" :class="{ 'image-message': message.messageType === 'image' }">
                  <!-- 文本消息 -->
                  <div v-if="message.messageType === 'text' || !message.messageType" class="text-content">
                    {{ getDisplayText(message) }}
                  </div>
                  <!-- 图片消息 -->
                  <div v-else-if="message.messageType === 'image'" class="image-content">
                    <div class="image-wrapper" @click="openImageInNewTab(getImageUrl(message))">
                      <img :src="getImageUrl(message)" class="message-image" @error="handleImageError" @load="handleImageLoad" alt="图片消息" />
                    </div>
                  </div>
                  <!-- 语音消息 -->
                  <div v-else-if="message.messageType === 'voice'" class="voice-content">
                    <div class="voice-wrapper" @click="playVoice(message)">
                      <el-icon class="voice-icon"><Microphone /></el-icon>
                      <span class="voice-text">{{ message.content || "[语音]" }}</span>
                      <span class="voice-duration">{{ getVoiceDuration(message) }}</span>
                    </div>
                  </div>
                  <!-- 其他类型消息 -->
                  <div v-else class="text-content">
                    {{ getDisplayText(message) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <div class="input-toolbar">
            <el-button size="small" circle icon="Picture" @click="handleSendImage" />
            <el-button size="small" circle icon="Microphone" @click="handleSendVoice" />
            <!-- <el-button size="small" circle icon="Paperclip" @click="handleSendFile" /> -->
          </div>
          <div class="input-area">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="轻按Enter键直接发送，按Shift+Enter组合键可以换行，支持Ctrl+V粘贴图片"
              @keydown="handleKeyDown"
              @paste="handlePaste"
              resize="none"
              ref="inputRef"
            />
          </div>
          <div class="send-area">
            <el-button type="primary" @click="handleSendMessage" :disabled="!inputMessage.trim() || sending" :loading="sending">
              {{ sending ? "发送中..." : "发送" }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog title="客服设置" v-model="settingsVisible" width="600px" :close-on-click-modal="false" draggable>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="欢迎语设置" name="welcome">
          <div class="welcome-settings">
            <el-form :model="welcomeForm" ref="welcomeFormRef" label-width="100px">
              <el-form-item label="欢迎语">
                <el-input
                  v-model="welcomeForm.welcomeMessage"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入欢迎语，留空则不自动发送欢迎语。设置后将在用户第一次咨询时自动发送。"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveWelcomeMessage" :loading="savingWelcome"> 保存设置 </el-button>
                <el-button @click="resetWelcomeMessage"> 重置 </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="显示设置" name="display">
          <div class="display-settings">
            <el-form :model="displayForm" ref="displayFormRef" label-width="120px">
              <el-form-item label="删除线字体">
                <el-switch v-model="displayForm.strikethroughEnabled" active-text="开启" inactive-text="关闭" />
                <div class="form-item-tip">开启后，客服发送的文字消息将自动转换为删除线字体（如：Z̸X̸安̸师̸大̸）</div>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveDisplaySettings" :loading="savingDisplay"> 保存设置 </el-button>
                <el-button @click="resetDisplaySettings"> 重置 </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup name="WxService">
import { listMyWxConfig, getWxMessages, sendWxMessage, getConversations, markConversationAsRead } from "@/api/wx/service";
import { getWelcomeMessage, updateWelcomeMessage, getStrikethroughSetting, updateStrikethroughSetting } from "@/api/wx";
import { listNotice } from "@/api/system/notice";
import { ChatDotRound, ZoomIn, Bell, DocumentRemove } from "@element-plus/icons-vue";
import request from "@/utils/request";

const { proxy } = getCurrentInstance();

// 会话列表相关
const conversations = ref([]);
const filteredConversations = ref([]);
const searchKeyword = ref("");
const currentConversation = ref(null);

// 消息相关
const currentMessages = ref([]);
const inputMessage = ref("");
const messageListRef = ref(null);
const inputRef = ref(null);

// 加载状态
const loading = ref(false);
const messageLoading = ref(false);
const sending = ref(false);

// 拖拽相关
const isDragOver = ref(false);

// 设置相关
const settingsVisible = ref(false);
const activeTab = ref("welcome");
const welcomeForm = reactive({
  welcomeMessage: "",
});
const welcomeFormRef = ref(null);
const savingWelcome = ref(false);

// 显示设置相关
const displayForm = reactive({
  strikethroughEnabled: true,
});
const displayFormRef = ref(null);
const savingDisplay = ref(false);

// 通知公告相关
const latestNotice = ref(null);

// 定时器
let refreshTimer = null;
let messageRefreshTimer = null;

// 初始化数据
onMounted(() => {
  loadConversations();
  loadLatestNotice();
  // 设置定时刷新，每5秒刷新一次会话列表
  refreshTimer = setInterval(() => {
    loadConversations();
  }, 5000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
  if (messageRefreshTimer) {
    clearInterval(messageRefreshTimer);
    messageRefreshTimer = null;
  }
});

// 加载会话列表
async function loadConversations() {
  loading.value = true;
  try {
    const response = await getConversations();
    conversations.value = response.data || [];
    filteredConversations.value = conversations.value;
  } catch (error) {
    console.error("加载会话列表失败:", error);
    proxy.$modal.msgError("加载会话列表失败: " + error.message);
    conversations.value = [];
    filteredConversations.value = [];
  } finally {
    loading.value = false;
  }
}

// 搜索会话
function handleSearch() {
  if (!searchKeyword.value.trim()) {
    filteredConversations.value = conversations.value;
  } else {
    filteredConversations.value = conversations.value.filter(
      (conv) => conv.userName.toLowerCase().includes(searchKeyword.value.toLowerCase()) || conv.appName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }
}

// 选择会话
async function selectConversation(conversation) {
  // 清除之前的消息刷新定时器
  if (messageRefreshTimer) {
    clearInterval(messageRefreshTimer);
    messageRefreshTimer = null;
  }

  currentConversation.value = conversation;

  // 加载该会话的消息记录
  await loadMessages(conversation.configId, conversation.id);

  // 加载显示设置（确保删除线设置是最新的）
  await loadDisplaySettings();

  // 清除未读数
  if (conversation.unreadCount > 0) {
    conversation.unreadCount = 0;
    // 调用API标记为已读
    try {
      await markAsRead(conversation.id);
    } catch (error) {
      console.error("标记已读失败:", error);
    }
  }

  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });

  // 设置消息刷新定时器，每5秒刷新一次当前会话的消息
  messageRefreshTimer = setInterval(() => {
    if (currentConversation.value && currentConversation.value.id === conversation.id) {
      loadMessages(conversation.configId, conversation.id);
    }
  }, 5000);
}

// 加载消息记录
async function loadMessages(configId, conversationId) {
  messageLoading.value = true;
  try {
    const response = await getWxMessages(configId, conversationId);
    currentMessages.value = response.data || [];
  } catch (error) {
    console.error("加载消息失败:", error);
    proxy.$modal.msgError("加载消息失败: " + error.message);
    currentMessages.value = [];
  } finally {
    messageLoading.value = false;
  }
}

// 标记会话为已读
async function markAsRead(conversationId) {
  try {
    await markConversationAsRead(conversationId);
  } catch (error) {
    console.error("标记已读失败:", error);
  }
}

// 滚动到底部
function scrollToBottom() {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
  }
}

// 格式化时间
function formatTime(time) {
  if (!time) return "";

  const now = new Date();
  const messageTime = new Date(time);

  // 检查时间是否有效
  if (isNaN(messageTime.getTime())) return "";

  const diff = now - messageTime;

  if (diff < 60000) {
    // 1分钟内
    return "刚刚";
  } else if (diff < 3600000) {
    // 1小时内
    return Math.floor(diff / 60000) + "分钟前";
  } else if (diff < 86400000) {
    // 24小时内
    return messageTime.toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" });
  } else {
    return messageTime.toLocaleDateString("zh-CN", { month: "2-digit", day: "2-digit" });
  }
}

// 格式化消息时间
function formatMessageTime(time) {
  if (!time) return "";

  const messageTime = new Date(time);

  // 检查时间是否有效
  if (isNaN(messageTime.getTime())) return "";

  return messageTime.toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// 是否显示时间
function shouldShowTime(message) {
  const messages = currentMessages.value;
  const index = messages.findIndex((m) => m.id === message.id);
  if (index === 0) return true;

  const prevMessage = messages[index - 1];

  // 检查时间是否有效
  const currentTime = new Date(message.createTime);
  const prevTime = new Date(prevMessage.createTime);

  if (isNaN(currentTime.getTime()) || isNaN(prevTime.getTime())) {
    return true;
  }

  const timeDiff = currentTime - prevTime;
  return timeDiff > 300000; // 5分钟
}

// 键盘事件处理
function handleKeyDown(event) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleSendMessage();
  }
}

// 删除线字符 (Unicode combining character)
const STRIKETHROUGH_CHAR = "\u0338";

// 转换文本为删除线字体
function convertToStrikethroughText(text) {
  if (!displayForm.strikethroughEnabled) {
    return text;
  }

  return text
    .split("")
    .map((char) => {
      // 跳过空格、换行符和标点符号
      if (char === " " || char === "\n" || char === "\r" || char === "\t") {
        return char;
      }
      return char + STRIKETHROUGH_CHAR;
    })
    .join("");
}

// 从删除线文本中提取原文
function extractOriginalText(strikethroughText) {
  if (!strikethroughText) return "";

  // 移除所有删除线字符
  return strikethroughText.replace(new RegExp(STRIKETHROUGH_CHAR, "g"), "");
}

// 检查消息是否包含删除线字符
function hasStrikethrough(message) {
  return !message.fromUser && message.content && message.content.includes(STRIKETHROUGH_CHAR);
}

// 获取要显示的文本内容
function getDisplayText(message) {
  if (!message || !message.content) {
    return "";
  }

  // 如果是客服发送的消息且包含删除线字符，显示原文
  if (!message.fromUser && hasStrikethrough(message)) {
    return extractOriginalText(message.content);
  }

  // 其他情况直接显示原内容
  return message.content;
}

// 发送消息
async function handleSendMessage() {
  if (!inputMessage.value.trim() || !currentConversation.value || sending.value) return;

  const originalContent = inputMessage.value.trim();
  const conversation = currentConversation.value;

  // 如果启用了删除线，转换消息内容
  let messageContent = originalContent;
  if (displayForm.strikethroughEnabled) {
    messageContent = convertToStrikethroughText(originalContent);
  }

  // 先清空输入框
  inputMessage.value = "";
  sending.value = true;

  try {
    // 调用发送消息API
    const messageData = {
      openId: conversation.openId,
      content: messageContent,
      messageType: "text",
    };

    await sendWxMessage(conversation.configId, messageData);

    // 发送成功后，重新加载消息列表以获取最新消息
    await loadMessages(conversation.configId, conversation.id);

    // 更新会话最后消息（显示原始文本，不带删除线）
    conversation.lastMessage = originalContent;
    conversation.lastMessageTime = new Date();

    nextTick(() => {
      scrollToBottom();
    });
  } catch (error) {
    console.error("发送消息失败:", error);
    proxy.$modal.msgError("发送消息失败: " + error.message);
    // 发送失败时恢复输入框内容（原始文本）
    inputMessage.value = originalContent;
  } finally {
    sending.value = false;
  }
}

// 发送图片
function handleSendImage() {
  if (!currentConversation.value) {
    proxy.$modal.msgError("请先选择一个会话");
    return;
  }

  // 创建文件输入元素
  const input = document.createElement("input");
  input.type = "file";
  input.accept = "image/*";
  input.style.display = "none";

  input.onchange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      proxy.$modal.msgError("请选择图片文件");
      return;
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      proxy.$modal.msgError("图片大小不能超过10MB");
      return;
    }

    await uploadAndSendImage(file);
  };

  // 触发文件选择
  document.body.appendChild(input);
  input.click();
  document.body.removeChild(input);
}

// 上传并发送图片
async function uploadAndSendImage(file) {
  const conversation = currentConversation.value;
  if (!conversation) return;

  // 显示上传进度
  const loadingInstance = proxy.$loading({
    text: "正在上传图片...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // 创建FormData
    const formData = new FormData();
    formData.append("file", file);

    // 上传图片到COS
    const uploadResponse = await request({
      url: "/common/upload-cos",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      params: {
        folder: "customer-service", // 客服上传的图片放在专门的文件夹
      },
    });

    if (uploadResponse && uploadResponse.success && uploadResponse.url) {
      // 使用COS返回的完整URL
      const imageUrl = uploadResponse.url;

      // 发送图片消息
      const messageData = {
        openId: conversation.openId,
        content: imageUrl,
        messageType: "image",
      };

      await sendWxMessage(conversation.configId, messageData);

      // 发送成功后，重新加载消息列表
      await loadMessages(conversation.configId, conversation.id);

      // 更新会话最后消息
      conversation.lastMessage = "[图片]";
      conversation.lastMessageTime = new Date();

      nextTick(() => {
        scrollToBottom();
      });

      proxy.$modal.msgSuccess("图片发送成功");
    } else {
      throw new Error("上传失败，未返回文件路径");
    }
  } catch (error) {
    console.error("上传图片失败:", error);
    proxy.$modal.msgError("图片发送失败: " + (error.message || "未知错误"));
  } finally {
    loadingInstance.close();
  }
}

// 发送语音
function handleSendVoice() {
  if (!currentConversation.value) {
    proxy.$modal.msgError("请先选择一个会话");
    return;
  }

  // 创建文件输入元素
  const input = document.createElement("input");
  input.type = "file";
  input.accept = "audio/*";
  input.style.display = "none";

  input.onchange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith("audio/")) {
      proxy.$modal.msgError("请选择音频文件");
      return;
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      proxy.$modal.msgError("音频文件大小不能超过10MB");
      return;
    }

    await uploadAndSendVoice(file);
  };

  // 触发文件选择
  document.body.appendChild(input);
  input.click();
  document.body.removeChild(input);
}

// 上传并发送语音
async function uploadAndSendVoice(file) {
  const conversation = currentConversation.value;
  if (!conversation) return;

  // 显示上传进度
  const loadingInstance = proxy.$loading({
    text: "正在上传语音...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // 创建FormData
    const formData = new FormData();
    formData.append("file", file);

    // 上传语音到COS
    const uploadResponse = await request({
      url: "/common/upload-cos",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      params: {
        folder: "customer-service-voice", // 客服语音专用文件夹
      },
    });

    if (uploadResponse && uploadResponse.success && uploadResponse.url) {
      // 使用COS返回的完整URL
      const voiceUrl = uploadResponse.url;

      // 发送语音消息
      const messageData = {
        openId: conversation.openId,
        content: voiceUrl,
        messageType: "voice",
      };

      await sendWxMessage(conversation.configId, messageData);

      // 发送成功后，重新加载消息列表
      await loadMessages(conversation.configId, conversation.id);

      // 更新会话最后消息
      conversation.lastMessage = "[语音]";
      conversation.lastMessageTime = new Date();

      nextTick(() => {
        scrollToBottom();
      });

      proxy.$modal.msgSuccess("语音发送成功");
    } else {
      throw new Error("上传失败，未返回文件路径");
    }
  } catch (error) {
    console.error("上传语音失败:", error);
    proxy.$modal.msgError("语音发送失败: " + (error.message || "未知错误"));
  } finally {
    loadingInstance.close();
  }
}

// 发送文件
function handleSendFile() {
  proxy.$modal.msgInfo("发送文件功能开发中...");
}

// 获取图片URL
function getImageUrl(message) {
  if (!message || !message.content) return "";

  const content = message.content;

  // 如果已经是完整的URL（旧的用户图片数据），直接返回
  if (content.startsWith("http://") || content.startsWith("https://")) {
    return content;
  }

  // 如果是相对路径（新的用户图片和客服图片），需要添加API前缀
  if (content.startsWith("/upload/")) {
    return import.meta.env.VITE_APP_BASE_API + content;
  }

  // 其他情况，尝试添加前缀
  return import.meta.env.VITE_APP_BASE_API + (content.startsWith("/") ? content : "/" + content);
}

// 在新标签页中打开图片
function openImageInNewTab(imageUrl) {
  if (imageUrl) {
    window.open(imageUrl, "_blank");
  }
}

// 播放语音
function playVoice(message) {
  if (!message.voiceUrl && !message.content) {
    proxy.$modal.msgError("语音文件不存在");
    return;
  }

  // 获取语音文件URL
  const voiceUrl = getVoiceUrl(message);
  if (!voiceUrl) {
    proxy.$modal.msgError("无法获取语音文件");
    return;
  }

  // 创建音频元素并播放
  const audio = new Audio(voiceUrl);
  audio.play().catch((error) => {
    console.error("播放语音失败:", error);
    proxy.$modal.msgError("播放语音失败");
  });
}

// 获取语音URL
function getVoiceUrl(message) {
  if (!message) return "";

  // 如果有voiceUrl字段，优先使用
  if (message.voiceUrl) {
    // 如果已经是完整的URL，直接返回
    if (message.voiceUrl.startsWith("http://") || message.voiceUrl.startsWith("https://")) {
      return message.voiceUrl;
    }
    // 如果是相对路径，添加API前缀
    return import.meta.env.VITE_APP_BASE_API + (message.voiceUrl.startsWith("/") ? message.voiceUrl : "/" + message.voiceUrl);
  }

  // 如果content是完整URL，使用content
  if (message.content && (message.content.startsWith("http://") || message.content.startsWith("https://"))) {
    return message.content;
  }

  return "";
}

// 获取语音时长（暂时返回固定值，实际应该从音频文件获取）
function getVoiceDuration(message) {
  // 这里可以根据实际需求实现获取音频时长的逻辑
  // 目前返回固定的时长显示
  return "0:05";
}

// 处理图片加载错误
function handleImageError(event) {
  console.error("图片加载失败:", event.target.src);
  // 显示错误占位符
  event.target.style.display = "none";
  const errorDiv = document.createElement("div");
  errorDiv.className = "image-error";
  errorDiv.textContent = "图片加载失败";
  errorDiv.style.cssText = `
    width: 150px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #999;
    font-size: 12px;
    border-radius: 8px;
    border: 1px dashed #ddd;
  `;
  event.target.parentNode.appendChild(errorDiv);
}

// 处理图片加载完成
function handleImageLoad(event) {
  // 图片加载完成后，确保容器大小适应图片
  const img = event.target;
  const wrapper = img.parentNode;

  // 获取图片的自然尺寸
  const naturalWidth = img.naturalWidth;
  const naturalHeight = img.naturalHeight;

  // 计算合适的显示尺寸
  const maxWidth = 300;
  const maxHeight = 300;
  const minWidth = 150;
  const minHeight = 100;

  let displayWidth = naturalWidth;
  let displayHeight = naturalHeight;

  // 如果图片太大，按比例缩小
  if (naturalWidth > maxWidth || naturalHeight > maxHeight) {
    const widthRatio = maxWidth / naturalWidth;
    const heightRatio = maxHeight / naturalHeight;
    const ratio = Math.min(widthRatio, heightRatio);

    displayWidth = naturalWidth * ratio;
    displayHeight = naturalHeight * ratio;
  }

  // 如果图片太小，设置最小尺寸
  if (displayWidth < minWidth) {
    displayWidth = minWidth;
  }
  if (displayHeight < minHeight) {
    displayHeight = minHeight;
  }

  // 应用计算出的尺寸
  img.style.width = displayWidth + "px";
  img.style.height = displayHeight + "px";
}

// 打开设置对话框
async function openSettings() {
  settingsVisible.value = true;
  // 加载当前欢迎语设置
  await loadWelcomeMessage();
  // 加载当前显示设置
  await loadDisplaySettings();
}

// 结束会话
function handleEndConversation() {
  proxy.$modal.confirm("确定要结束当前会话吗？").then(() => {
    proxy.$modal.msgSuccess("会话已结束");
    // 这里可以调用结束会话的API
  });
}

// 处理拖拽进入
function handleDragEnter(event) {
  event.preventDefault();
  event.stopPropagation();

  if (!currentConversation.value) return;

  // 检查是否包含文件
  if (event.dataTransfer.types.includes("Files")) {
    isDragOver.value = true;
  }
}

// 处理拖拽悬停
function handleDragOver(event) {
  event.preventDefault();
  event.stopPropagation();

  if (!currentConversation.value) return;

  if (event.dataTransfer.types.includes("Files")) {
    event.dataTransfer.dropEffect = "copy";
  }
}

// 处理拖拽离开
function handleDragLeave(event) {
  event.preventDefault();
  event.stopPropagation();

  // 检查是否真的离开了拖拽区域
  const rect = event.currentTarget.getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false;
  }
}

// 处理文件拖拽放置
function handleDrop(event) {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = false;

  if (!currentConversation.value) {
    proxy.$modal.msgError("请先选择一个会话");
    return;
  }

  const files = Array.from(event.dataTransfer.files);
  const imageFiles = files.filter((file) => file.type.startsWith("image/"));

  if (imageFiles.length === 0) {
    proxy.$modal.msgError("请拖拽图片文件");
    return;
  }

  // 处理第一个图片文件
  const file = imageFiles[0];

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    proxy.$modal.msgError("图片大小不能超过5MB");
    return;
  }

  uploadAndSendImage(file);
}

// 处理粘贴事件
function handlePaste(event) {
  if (!currentConversation.value) {
    proxy.$modal.msgError("请先选择一个会话");
    return;
  }

  const clipboardData = event.clipboardData || window.clipboardData;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    // 检查是否为图片
    if (item.type.startsWith("image/")) {
      event.preventDefault(); // 阻止默认粘贴行为

      const file = item.getAsFile();
      if (file) {
        // 验证文件大小 (5MB)
        if (file.size > 5 * 1024 * 1024) {
          proxy.$modal.msgError("图片大小不能超过5MB");
          return;
        }

        uploadAndSendImage(file);
      }
      break;
    }
  }
}

// 加载欢迎语
async function loadWelcomeMessage() {
  try {
    // 获取我的配置列表
    const configResponse = await listMyWxConfig();
    const configs = configResponse.data.list || [];

    if (configs.length === 0) {
      return;
    }

    // 有配置时，优先从服务器加载，如果服务器没有则使用本地存储
    const firstConfig = configs[0];
    const response = await getWelcomeMessage(firstConfig.id);
    welcomeForm.welcomeMessage = response.data.welcomeMessage || "";
  } catch (error) {
    console.error("加载欢迎语失败:", error);
  }
}

// 保存欢迎语
async function saveWelcomeMessage() {
  savingWelcome.value = true;
  try {
    // 获取我的配置列表
    const configResponse = await listMyWxConfig();
    const configs = configResponse.data.list || [];
    if (configs.length === 0) {
      // 即使没有配置也保存欢迎语设置到本地存储
      proxy.$modal.msgSuccess("欢迎语设置保存成功");
      return;
    }
    // 为所有配置设置相同的欢迎语
    const updatePromises = configs.map((config) => {
      return updateWelcomeMessage(config.id, {
        welcomeMessage: welcomeForm.welcomeMessage,
      });
    });

    const results = await Promise.all(updatePromises);
    proxy.$modal.msgSuccess(`欢迎语设置保存成功`);
  } catch (error) {
    proxy.$modal.msgError("保存欢迎语失败: " + error.message);
  } finally {
    savingWelcome.value = false;
  }
}

// 重置欢迎语
function resetWelcomeMessage() {
  // 重新加载
  loadWelcomeMessage();
}

// 加载显示设置
async function loadDisplaySettings() {
  try {
    // 获取我的配置列表
    const configResponse = await listMyWxConfig();
    const configs = configResponse.data.list || [];

    if (configs.length === 0) {
      return;
    }

    // 有配置时，从服务器加载删除线设置
    const firstConfig = configs[0];
    const response = await getStrikethroughSetting(firstConfig.id);
    displayForm.strikethroughEnabled = response.data.strikethroughEnabled !== undefined ? response.data.strikethroughEnabled : true;
  } catch (error) {
    console.error("加载显示设置失败:", error);
  }
}

// 保存显示设置
async function saveDisplaySettings() {
  savingDisplay.value = true;
  try {
    // 获取我的配置列表
    const configResponse = await listMyWxConfig();
    const configs = configResponse.data.list || [];
    if (configs.length === 0) {
      proxy.$modal.msgSuccess("显示设置保存成功");
      return;
    }
    // 为所有配置设置相同的显示设置
    const updatePromises = configs.map((config) => {
      return updateStrikethroughSetting(config.id, {
        strikethroughEnabled: displayForm.strikethroughEnabled,
      });
    });

    const results = await Promise.all(updatePromises);
    proxy.$modal.msgSuccess(`显示设置保存成功`);
  } catch (error) {
    proxy.$modal.msgError("保存显示设置失败: " + error.message);
  } finally {
    savingDisplay.value = false;
  }
}

// 重置显示设置
function resetDisplaySettings() {
  // 重新加载
  loadDisplaySettings();
  loadLatestNotice();
}

// 加载最新通知公告
async function loadLatestNotice() {
  try {
    const response = await listNotice({
      pageNum: 1,
      pageSize: 1,
      status: 0 // 只获取正常状态的公告
    });

    if (response.rows && response.rows.length > 0) {
      latestNotice.value = response.rows[0];
    }
  } catch (error) {
    console.error("加载通知公告失败:", error);
  }
}

// 获取公告类型文本
function getNoticeTypeText(noticeType) {
  const typeMap = {
    '1': '通知',
    '2': '公告'
  };
  return typeMap[noticeType] || '通知';
}

// 格式化公告时间
function formatNoticeTime(time) {
  if (!time) return '';
  return proxy.parseTime(time, '{y}-{m}-{d} {h}:{i}');
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100%;
  background: #f5f5f5;
  position: relative;
}

/* 左侧会话列表 */
.chat-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: calc(100vh - 200px);
  z-index: 10;
}

.ad-main {
  position: fixed;
  left: 0;
  bottom: 0;
  height: 200px;
  width: 300px;
  z-index: 10;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.notice-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.notice-icon {
  color: #409eff;
  margin-right: 8px;
  font-size: 18px;
}

.notice-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  margin-right: 12px;
  flex: 1;
}

.notice-type {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.notice-body {
  flex: 1;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  overflow-y: auto;
  margin-bottom: 12px;
}

.notice-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #909399;
}

.no-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.no-notice-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
}

.empty-conversations {
  padding: 40px 20px;
  text-align: center;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  position: relative;
}

.conversation-item:hover {
  background-color: #f5f7fa;
}

.conversation-item.active {
  background-color: #e6f7ff;
}

.conversation-item .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.time {
  font-size: 12px;
  color: #909399;
}

.last-message {
  color: #606266;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

.app-name {
  color: #909399;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.unread-badge {
  position: absolute;
  top: 8px;
  right: 12px;
  background: #f56c6c;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  min-width: 18px;
  text-align: center;
}

/* 右侧聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin-left: 300px; /* 为左侧固定栏留出空间 */
}

.no-conversation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-message {
  text-align: center;
  color: #909399;
}

.welcome-message p {
  margin-top: 16px;
  font-size: 16px;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info .avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 12px;
}

.user-info .info .name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-info .info .app {
  font-size: 12px;
  color: #909399;
}

.message-list {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
  background: #f8f9fa;
  position: relative;
  transition: all 0.3s ease;
}

/* 拖拽悬停状态 */
.message-list.drag-over {
  background: #e6f7ff;
  border: 2px dashed #1890ff;
}

.message-list.drag-over::before {
  content: "拖拽图片到此处发送";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  z-index: 1000;
  pointer-events: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.empty-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.message-wrapper {
  margin-bottom: 16px;
}

.message-time {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin-bottom: 12px;
}

.message-item {
  display: flex;
  margin-bottom: 12px;
}

.message-item.is-self {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.message-item.is-self .message-avatar {
  background: #67c23a;
}

.message-content {
  max-width: 60%;
  margin: 0 8px;
}

.message-bubble {
  background: white;
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  position: relative;
}

.message-item.is-self .message-bubble {
  background: #409eff;
  color: white;
}

/* 图片消息样式 */
.message-bubble.image-message {
  padding: 4px;
  background: transparent;
  box-shadow: none;
}

.message-item.is-self .message-bubble.image-message {
  background: transparent;
}

.image-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-wrapper {
  position: relative;
  max-width: 300px;
  min-width: 150px;
  width: auto;
  height: auto;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: inline-block;
}

.image-wrapper:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-image {
  width: auto;
  height: auto;
  max-width: 300px;
  max-height: 300px;
  min-width: 150px;
  min-height: 100px;
  border: none;
  border-radius: 8px;
  background: #f5f5f5;
  display: block;
  object-fit: contain;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

/* 语音消息样式 */
.voice-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.voice-wrapper {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f0f0f0;
  border-radius: 20px;
  cursor: pointer;
  min-width: 120px;
  max-width: 200px;
  transition: background 0.2s ease;
}

.voice-wrapper:hover {
  background: #e0e0e0;
}

.voice-icon {
  color: #409eff;
  margin-right: 8px;
  font-size: 16px;
}

.voice-text {
  flex: 1;
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.voice-duration {
  font-size: 12px;
  color: #999;
}

/* 用户发送的语音消息样式 */
.message-item.is-self .voice-wrapper {
  background: #409eff;
  color: white;
}

.message-item.is-self .voice-wrapper .voice-icon {
  color: white;
}

.message-item.is-self .voice-wrapper .voice-text {
  color: white;
}

.message-item.is-self .voice-wrapper .voice-duration {
  color: rgba(255, 255, 255, 0.8);
}

.message-item.is-self .voice-wrapper:hover {
  background: #337ecc;
}

.message-image:hover {
  transform: scale(1.02);
}

.text-content {
  word-wrap: break-word;
  white-space: pre-wrap;
}

.chat-input {
  border-top: 1px solid #e4e7ed;
  background: white;
}

.input-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.input-area {
  padding: 0 16px;
}

.input-area :deep(.el-textarea__inner) {
  border: none;
  box-shadow: none;
  resize: none;
  padding: 12px 0;
}

.input-area :deep(.el-textarea__inner):focus {
  border: none;
  box-shadow: none;
}

.send-area {
  padding: 8px 16px;
  text-align: right;
}

/* 设置对话框样式 */
.welcome-settings,
.display-settings {
  padding: 20px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
